import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Question Component for individual quiz questions
const QuestionComponent = ({ question, selectedAnswer, onAnswerSelect, questionNumber, totalQuestions }) => {
  return (
    <div style={{ transition: 'all 0.5s ease-out' }}>
      <h1 style={{
        fontSize: '28px',
        fontWeight: 'bold',
        color: '#005A62',
        textAlign: 'center',
        marginBottom: '8px',
        lineHeight: '1.3'
      }}>
        Question {questionNumber} of {totalQuestions}
      </h1>

      <h2 style={{
        fontSize: '22px',
        fontWeight: '600',
        color: '#374151',
        textAlign: 'center',
        marginBottom: '32px',
        lineHeight: '1.4'
      }}>
        {question.question}
      </h2>

      {/* Answer Options */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        marginBottom: '40px'
      }}>
        {/* API Options */}
        {question.options.map((option) => (
          <label
            key={option.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: selectedAnswer === option.id ? '#FF6F61' : 'white',
              border: selectedAnswer === option.id ? '2px solid #FF6F61' : '2px solid #e5e7eb',
              borderRadius: '12px',
              padding: '16px 20px',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              transform: selectedAnswer === option.id ? 'scale(1.02)' : 'scale(1)',
              boxShadow: selectedAnswer === option.id ? '0 8px 25px -5px rgba(255, 111, 97, 0.3)' : '0 2px 4px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              if (selectedAnswer !== option.id) {
                e.target.style.borderColor = '#FF6F61';
                e.target.style.transform = 'scale(1.01)';
              }
            }}
            onMouseLeave={(e) => {
              if (selectedAnswer !== option.id) {
                e.target.style.borderColor = '#e5e7eb';
                e.target.style.transform = 'scale(1)';
              }
            }}
          >
            <input
              type="radio"
              name={`question-${question.id}`}
              value={option.id}
              checked={selectedAnswer === option.id}
              onChange={() => onAnswerSelect(option.id, option.points)}
              style={{
                marginRight: '12px',
                width: '18px',
                height: '18px',
                accentColor: '#FF6F61'
              }}
            />
            <span style={{
              fontSize: '16px',
              fontWeight: '500',
              color: selectedAnswer === option.id ? 'white' : '#374151',
              flex: 1
            }}>
              {option.option_text}
            </span>
          </label>
        ))}

        {/* None of These Option */}
        <label
          style={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: selectedAnswer === 'none' ? '#6b7280' : 'white',
            border: selectedAnswer === 'none' ? '2px solid #6b7280' : '2px solid #e5e7eb',
            borderRadius: '12px',
            padding: '16px 20px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: selectedAnswer === 'none' ? 'scale(1.02)' : 'scale(1)',
            boxShadow: selectedAnswer === 'none' ? '0 8px 25px -5px rgba(107, 114, 128, 0.3)' : '0 2px 4px -1px rgba(0, 0, 0, 0.1)'
          }}
          onMouseEnter={(e) => {
            if (selectedAnswer !== 'none') {
              e.target.style.borderColor = '#6b7280';
              e.target.style.transform = 'scale(1.01)';
            }
          }}
          onMouseLeave={(e) => {
            if (selectedAnswer !== 'none') {
              e.target.style.borderColor = '#e5e7eb';
              e.target.style.transform = 'scale(1)';
            }
          }}
        >
          <input
            type="radio"
            name={`question-${question.id}`}
            value="none"
            checked={selectedAnswer === 'none'}
            onChange={() => onAnswerSelect('none', 0)}
            style={{
              marginRight: '12px',
              width: '18px',
              height: '18px',
              accentColor: '#6b7280'
            }}
          />
          <span style={{
            fontSize: '16px',
            fontWeight: '500',
            color: selectedAnswer === 'none' ? 'white' : '#6b7280',
            flex: 1,
            fontStyle: 'italic'
          }}>
            None of These
          </span>
        </label>
      </div>
    </div>
  );
};

function QuizPage() {
  const navigate = useNavigate();

  // Quiz state management
  const [quizState, setQuizState] = useState('loading'); // 'loading', 'ready', 'taking', 'submitting', 'completed', 'error'
  const [allQuestions, setAllQuestions] = useState([]);
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({}); // {question_id: {option_id, points}}
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitError, setSubmitError] = useState('');

  // UI state
  const [timeRemaining, setTimeRemaining] = useState('');
  const [startTime, setStartTime] = useState(null);

  // Fetch questions from API on component mount
  useEffect(() => {
    fetchQuestions();
  }, []);

  // Timer effect for tracking quiz duration
  useEffect(() => {
    if (startTime && quizState === 'taking') {
      const timer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        setTimeRemaining(`${minutes}:${seconds.toString().padStart(2, '0')} elapsed`);
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [startTime, quizState]);

  // Fetch questions from the API
  const fetchQuestions = async () => {
    try {
      setLoading(true);
      setError('');
      const authToken = localStorage.getItem('authToken');

      if (!authToken) {
        setError('Authentication required. Please log in.');
        setQuizState('error');
        return;
      }

      console.log('Fetching quiz questions...');

      const response = await fetch('http://168.231.122.170/cstm-api/api/mcqs', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Questions fetched:', data.length);

        if (data.length === 0) {
          setError('No questions available. Please try again later.');
          setQuizState('error');
          return;
        }

        setAllQuestions(data);

        // Randomly select 20-25 questions (max 30 as specified)
        const numQuestions = Math.min(Math.max(20, Math.floor(Math.random() * 6) + 20), Math.min(30, data.length));
        const shuffled = [...data].sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, numQuestions);

        setSelectedQuestions(selected);
        setQuizState('ready');
        console.log(`Selected ${selected.length} questions for quiz`);
      } else {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        setError(`Failed to load questions (${response.status}). Please try again.`);
        setQuizState('error');
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
      setError('Network error. Please check your connection and try again.');
      setQuizState('error');
    } finally {
      setLoading(false);
    }
  };

  // Start the quiz
  const startQuiz = () => {
    setQuizState('taking');
    setStartTime(Date.now());
    setCurrentQuestionIndex(0);
    setAnswers({});
  };

  // Handle answer selection
  const handleAnswerSelect = (optionId, points) => {
    const currentQuestion = selectedQuestions[currentQuestionIndex];
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: {
        option_id: optionId,
        points: points
      }
    }));
  };

  // Navigation functions
  const goToNextQuestion = () => {
    if (currentQuestionIndex < selectedQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const skipQuestion = () => {
    // Remove answer for current question if it exists
    const currentQuestion = selectedQuestions[currentQuestionIndex];
    setAnswers(prev => {
      const newAnswers = { ...prev };
      delete newAnswers[currentQuestion.id];
      return newAnswers;
    });
    goToNextQuestion();
  };

  // Submit quiz answers
  const submitQuiz = async () => {
    try {
      setQuizState('submitting');
      setSubmitError('');
      const authToken = localStorage.getItem('authToken');

      // Prepare answers in the required format
      const formattedAnswers = Object.entries(answers).map(([questionId, answerData]) => ({
        question_id: parseInt(questionId),
        points: answerData.points
      }));

      console.log('Submitting quiz with answers:', formattedAnswers);

      // Set a timeout for the request (30 seconds)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await fetch('http://168.231.122.170/cstm-api/api/submit-quiz', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          answers: formattedAnswers
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const results = await response.json();
        console.log('Quiz results:', results);

        // Store results in localStorage for future use
        localStorage.setItem('quizResults', JSON.stringify(results));
        localStorage.setItem('quizCompletedAt', new Date().toISOString());

        // Check if results contain expected data
        if (results && (results.confidence !== undefined || results.moods || results.foods || results.book)) {
          // Valid results received - check confidence level
          if (results.confidence >= 70) {
            // High confidence - show personalized results (for now redirect to main menu with success message)
            localStorage.setItem('quizMessage', JSON.stringify({
              type: 'success',
              title: 'Quiz Completed Successfully!',
              message: `Great! We analyzed your responses with ${results.confidence}% confidence. Your personalized recommendations are ready!`
            }));
          } else {
            // Low confidence - general exploration
            localStorage.setItem('quizMessage', JSON.stringify({
              type: 'info',
              title: 'Quiz Completed!',
              message: 'Thank you for completing the quiz. We\'ll help you explore various options that might interest you!'
            }));
          }
        } else {
          // Results received but incomplete data
          localStorage.setItem('quizMessage', JSON.stringify({
            type: 'warning',
            title: 'Quiz Submitted',
            message: 'Your quiz has been submitted successfully. We\'re still processing your recommendations.'
          }));
        }

        // Navigate to food menu page
        navigate('/food-menu');
      } else {
        // Handle HTTP error responses
        let errorMessage = `Server error (${response.status})`;
        let messageType = 'error';
        let messageTitle = 'Quiz Submission Failed';

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }

        // Handle specific error cases
        if (response.status === 404) {
          messageType = 'warning';
          messageTitle = 'Quiz Submitted Successfully';
          errorMessage = 'Your quiz responses have been recorded. The recommendation system is currently being set up and will be available soon!';
        } else if (response.status === 401 || response.status === 403) {
          messageType = 'error';
          messageTitle = 'Authentication Error';
          errorMessage = 'Please log in again to submit your quiz.';
        }

        console.error('Submit Error:', response.status, errorMessage);

        // Store message and redirect to main menu
        localStorage.setItem('quizMessage', JSON.stringify({
          type: messageType,
          title: messageTitle,
          message: errorMessage
        }));

        navigate('/food-menu');
      }
    } catch (error) {
      console.error('Error submitting quiz:', error);

      let errorMessage = 'Network error. Please check your connection and try again.';

      if (error.name === 'AbortError') {
        errorMessage = 'Quiz submission timed out. Please try again with a better connection.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Store error message and redirect to main menu
      localStorage.setItem('quizMessage', JSON.stringify({
        type: 'error',
        title: 'Quiz Submission Error',
        message: errorMessage
      }));

      navigate('/food-menu');
    }
  };

  // Calculate progress
  const progressPercentage = selectedQuestions.length > 0
    ? ((currentQuestionIndex + 1) / selectedQuestions.length) * 100
    : 0;

  // Get current question
  const currentQuestion = selectedQuestions[currentQuestionIndex];
  const currentAnswer = currentQuestion ? answers[currentQuestion.id]?.option_id : null;

  // Check if quiz is complete
  const isLastQuestion = currentQuestionIndex === selectedQuestions.length - 1;

  // Handle next button click
  const handleNext = () => {
    if (isLastQuestion) {
      // Submit quiz
      submitQuiz();
    } else {
      goToNextQuestion();
    }
  };

  // Render different states
  if (loading) {
    return (
      <div style={{
        fontFamily: 'Poppins, sans-serif',
        background: 'linear-gradient(to bottom, #FFF8EE, #FFDFD1)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          padding: '48px',
          textAlign: 'center',
          maxWidth: '400px'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #FF6F61',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 24px'
          }}></div>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#005A62',
            marginBottom: '8px'
          }}>Loading Quiz</h2>
          <p style={{ color: '#6b7280' }}>Preparing your personalized questions...</p>
        </div>
      </div>
    );
  }

  if (quizState === 'error') {
    return (
      <div style={{
        fontFamily: 'Poppins, sans-serif',
        background: 'linear-gradient(to bottom, #FFF8EE, #FFDFD1)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          padding: '48px',
          textAlign: 'center',
          maxWidth: '500px'
        }}>
          <div style={{
            fontSize: '48px',
            color: '#dc2626',
            marginBottom: '24px'
          }}>⚠️</div>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#005A62',
            marginBottom: '16px'
          }}>Oops! Something went wrong</h2>
          <p style={{
            color: '#6b7280',
            marginBottom: '32px',
            lineHeight: '1.5'
          }}>{error}</p>
          <div style={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
            <button
              onClick={fetchQuestions}
              style={{
                backgroundColor: '#FF6F61',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                padding: '12px 24px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              Try Again
            </button>
            <button
              onClick={() => navigate('/food-menu')}
              style={{
                backgroundColor: 'white',
                color: '#374151',
                border: '2px solid #e5e7eb',
                borderRadius: '12px',
                padding: '12px 24px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              Back to Food Menu
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Quiz ready state - show start screen
  if (quizState === 'ready') {
    return (
      <div style={{
        fontFamily: 'Poppins, sans-serif',
        background: 'linear-gradient(to bottom, #FFF8EE, #FFDFD1)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          padding: '48px',
          textAlign: 'center',
          maxWidth: '600px'
        }}>
          <div style={{
            fontSize: '64px',
            marginBottom: '24px'
          }}>🧠</div>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            color: '#005A62',
            marginBottom: '16px'
          }}>Ready to Start Your Quiz?</h1>
          <p style={{
            color: '#6b7280',
            marginBottom: '24px',
            fontSize: '18px',
            lineHeight: '1.6'
          }}>
            We've prepared {selectedQuestions.length} personalized questions to understand your mood and preferences.
            This will help us recommend the perfect food and books for your emotional well-being.
          </p>
          <div style={{
            backgroundColor: '#f9fafb',
            borderRadius: '12px',
            padding: '20px',
            marginBottom: '32px',
            textAlign: 'left'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '12px'
            }}>📋 Quiz Guidelines:</h3>
            <ul style={{
              color: '#6b7280',
              fontSize: '14px',
              lineHeight: '1.5',
              paddingLeft: '20px'
            }}>
              <li>Answer honestly for the best recommendations</li>
              <li>You can skip questions if needed</li>
              <li>Navigate back and forth between questions</li>
              <li>Take your time - there's no rush</li>
            </ul>
          </div>
          <button
            onClick={startQuiz}
            style={{
              backgroundColor: '#FF6F61',
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              padding: '16px 32px',
              fontSize: '18px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 12px rgba(255, 111, 97, 0.3)'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'scale(1.05)';
              e.target.style.boxShadow = '0 8px 25px rgba(255, 111, 97, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'scale(1)';
              e.target.style.boxShadow = '0 4px 12px rgba(255, 111, 97, 0.3)';
            }}
          >
            Start Quiz 🚀
          </button>
        </div>
      </div>
    );
  }

  // Quiz submitting state
  if (quizState === 'submitting') {
    return (
      <div style={{
        fontFamily: 'Poppins, sans-serif',
        background: 'linear-gradient(to bottom, #FFF8EE, #FFDFD1)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          padding: '48px',
          textAlign: 'center',
          maxWidth: '400px'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #FF6F61',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 24px'
          }}></div>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#005A62',
            marginBottom: '8px'
          }}>Analyzing Your Responses</h2>
          <p style={{ color: '#6b7280' }}>Creating your personalized recommendations...</p>
          {submitError && (
            <div style={{
              marginTop: '20px',
              padding: '12px',
              backgroundColor: '#fee2e2',
              borderRadius: '8px',
              color: '#dc2626',
              fontSize: '14px'
            }}>
              {submitError}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Main quiz taking interface
  return (
    <div style={{
      fontFamily: 'Poppins, sans-serif',
      background: 'linear-gradient(to bottom, #FFF8EE, #FFDFD1)',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '16px',
      position: 'relative'
    }}>
      {/* Floating particles */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        pointerEvents: 'none',
        zIndex: 1
      }}>
        <div style={{
          position: 'absolute',
          top: '25%',
          left: '20%',
          width: '16px',
          height: '16px',
          borderRadius: '50%',
          backgroundColor: '#FF6F61',
          opacity: 0.2,
          animation: 'float 6s ease-in-out infinite'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '33%',
          right: '25%',
          width: '24px',
          height: '24px',
          borderRadius: '50%',
          backgroundColor: '#006D77',
          opacity: 0.1,
          animation: 'float 6s ease-in-out 2s infinite'
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '25%',
          left: '33%',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          backgroundColor: '#FF6F61',
          opacity: 0.15,
          animation: 'float 6s ease-in-out infinite'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '66%',
          right: '33%',
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          backgroundColor: '#006D77',
          opacity: 0.2,
          animation: 'float 6s ease-in-out 2s infinite'
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '33%',
          right: '20%',
          width: '16px',
          height: '16px',
          borderRadius: '50%',
          backgroundColor: '#FF6F61',
          opacity: 0.1,
          animation: 'float 6s ease-in-out infinite'
        }}></div>
      </div>

      {/* Quiz Container */}
      <div style={{
        width: '100%',
        maxWidth: '700px',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(8px)',
        borderRadius: '24px',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        padding: '32px',
        position: 'relative',
        overflow: 'hidden',
        zIndex: 2
      }}>
        {/* Header with Progress */}
        <div style={{ marginBottom: '32px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '8px'
          }}>
            <div style={{
              fontSize: '14px',
              fontWeight: '500',
              color: '#006D77'
            }}>
              <span>{currentQuestionIndex + 1}</span>/<span>{selectedQuestions.length}</span>
            </div>
            <div style={{
              fontSize: '14px',
              fontWeight: '500',
              color: '#006D77',
              display: 'flex',
              alignItems: 'center'
            }}>
              <i className="fa-regular fa-clock" style={{ marginRight: '8px' }}></i>
              <span>{timeRemaining || 'Just started'}</span>
            </div>
          </div>
          <div style={{
            height: '8px',
            width: '100%',
            backgroundColor: '#e5e7eb',
            borderRadius: '9999px',
            overflow: 'hidden'
          }}>
            <div style={{
              height: '100%',
              width: `${progressPercentage}%`,
              backgroundColor: '#FF6F61',
              borderRadius: '9999px',
              transition: 'all 0.5s ease-out'
            }}></div>
          </div>
        </div>

        {/* Quiz Content */}
        {currentQuestion && (
          <QuestionComponent
            question={currentQuestion}
            selectedAnswer={currentAnswer}
            onAnswerSelect={handleAnswerSelect}
            questionNumber={currentQuestionIndex + 1}
            totalQuestions={selectedQuestions.length}
          />
        )}

        {/* Navigation Buttons */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: '32px'
        }}>
          <button
            onClick={goToPreviousQuestion}
            disabled={currentQuestionIndex === 0}
            style={{
              backgroundColor: currentQuestionIndex === 0 ? '#f3f4f6' : 'white',
              color: currentQuestionIndex === 0 ? '#9ca3af' : '#374151',
              border: '2px solid #e5e7eb',
              borderRadius: '12px',
              padding: '12px 24px',
              fontWeight: '600',
              cursor: currentQuestionIndex === 0 ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            <i className="fa-solid fa-arrow-left" style={{ marginRight: '8px' }}></i>
            Previous
          </button>

          <button
            onClick={skipQuestion}
            style={{
              backgroundColor: 'transparent',
              color: '#6b7280',
              border: 'none',
              padding: '12px 24px',
              fontWeight: '500',
              cursor: 'pointer',
              textDecoration: 'underline',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.color = '#374151';
            }}
            onMouseLeave={(e) => {
              e.target.style.color = '#6b7280';
            }}
          >
            Skip this question
          </button>

          <button
            onClick={handleNext}
            style={{
              backgroundColor: '#FF6F61',
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              padding: '12px 24px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'scale(1.05)';
              e.target.style.boxShadow = '0 8px 25px rgba(255, 111, 97, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'scale(1)';
              e.target.style.boxShadow = 'none';
            }}
          >
            {isLastQuestion ? (
              <>
                Submit Quiz
                <i className="fa-solid fa-check" style={{ marginLeft: '8px' }}></i>
              </>
            ) : (
              <>
                Next
                <i className="fa-solid fa-arrow-right" style={{ marginLeft: '8px' }}></i>
              </>
            )}
          </button>
        </div>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
}

export default QuizPage;
