import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { booksAPI, subMoodsAPI } from '../../services/api';
import DashboardSidebar from '../../components/DashboardSidebar';

function Books() {
  const navigate = useNavigate();
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedMood, setSelectedMood] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [userName, setUserName] = useState('Aisha');

  // CRUD form states
  const [showCrudForm, setShowCrudForm] = useState(false);
  const [formMode, setFormMode] = useState('add'); // 'add', 'edit'
  const [editingBook, setEditingBook] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    description: '',
    sub_moods: [],
    cover_image: ''
  });
  const [subMoods, setSubMoods] = useState([]);
  const [formLoading, setFormLoading] = useState(false);
  const [formMessage, setFormMessage] = useState('');

  // Form field focus states
  const [titleFocused, setTitleFocused] = useState(false);
  const [authorFocused, setAuthorFocused] = useState(false);
  const [descriptionFocused, setDescriptionFocused] = useState(false);
  const [subMoodsFocused, setSubMoodsFocused] = useState(false);
  const [imageFocused, setImageFocused] = useState(false);

  // Form field hover states
  const [titleHovered, setTitleHovered] = useState(false);
  const [authorHovered, setAuthorHovered] = useState(false);
  const [descriptionHovered, setDescriptionHovered] = useState(false);
  const [subMoodsHovered, setSubMoodsHovered] = useState(false);
  const [imageHovered, setImageHovered] = useState(false);
  const [submitHovered, setSubmitHovered] = useState(false);
  const [cancelHovered, setCancelHovered] = useState(false);

  // Mood categories
  const moodCategories = [
    { id: 'all', name: 'All Books', color: '#6b7280' },
    { id: 'stressed', name: 'Feeling Stressed', color: '#ef4444' },
    { id: 'energy', name: 'Need Energy', color: '#eab308' },
    { id: 'comfort', name: 'Seeking Comfort', color: '#a855f7' },
    { id: 'focus', name: 'Need Focus', color: '#3b82f6' },
    { id: 'calm', name: 'Want Calm', color: '#10b981' }
  ];

  // CRUD form styles (matching login form)
  const formStyles = {
    overlay: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 1000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '16px'
    },
    formCard: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '24px',
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      padding: '48px',
      width: '100%',
      maxWidth: '500px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      maxHeight: '90vh',
      overflowY: 'auto'
    },
    title: {
      fontSize: '24px',
      fontWeight: 'bold',
      color: '#006D77',
      marginBottom: '24px',
      textAlign: 'center'
    },
    form: {
      display: 'flex',
      flexDirection: 'column',
      gap: '20px'
    },
    fieldContainer: {
      display: 'flex',
      flexDirection: 'column',
      gap: '8px'
    },
    label: {
      display: 'block',
      fontSize: '14px',
      fontWeight: '600',
      color: '#006D77'
    },
    inputContainer: {
      position: 'relative',
      transition: 'transform 0.2s ease'
    },
    inputContainerHover: {
      transform: 'scale(1.02)'
    },
    input: {
      width: '100%',
      padding: '16px',
      backgroundColor: '#f9fafb',
      border: '1px solid rgba(0, 109, 119, 0.3)',
      borderRadius: '12px',
      outline: 'none',
      transition: 'all 0.2s ease',
      color: '#111827',
      fontSize: '16px',
      boxSizing: 'border-box'
    },
    textarea: {
      width: '100%',
      padding: '16px',
      backgroundColor: '#f9fafb',
      border: '1px solid rgba(0, 109, 119, 0.3)',
      borderRadius: '12px',
      outline: 'none',
      transition: 'all 0.2s ease',
      color: '#111827',
      fontSize: '16px',
      boxSizing: 'border-box',
      minHeight: '100px',
      resize: 'vertical'
    },
    inputFocus: {
      boxShadow: '0 0 0 3px rgba(255, 111, 97, 0.2)',
      borderColor: '#FF6F61'
    },
    select: {
      width: '100%',
      padding: '16px',
      backgroundColor: '#f9fafb',
      border: '1px solid rgba(0, 109, 119, 0.3)',
      borderRadius: '12px',
      outline: 'none',
      transition: 'all 0.2s ease',
      color: '#111827',
      fontSize: '16px',
      boxSizing: 'border-box'
    },
    buttonContainer: {
      display: 'flex',
      gap: '12px',
      marginTop: '8px'
    },
    submitButton: {
      flex: 1,
      backgroundColor: '#FF6F61',
      color: 'white',
      fontWeight: 'bold',
      padding: '16px 24px',
      borderRadius: '12px',
      border: 'none',
      cursor: 'pointer',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.2s ease',
      fontSize: '16px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px'
    },
    submitButtonHover: {
      backgroundColor: 'rgba(255, 111, 97, 0.9)',
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      transform: 'scale(1.02)'
    },
    cancelButton: {
      flex: 1,
      backgroundColor: 'transparent',
      color: '#006D77',
      fontWeight: 'bold',
      padding: '16px 24px',
      borderRadius: '12px',
      border: '2px solid #006D77',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      fontSize: '16px'
    },
    cancelButtonHover: {
      backgroundColor: 'rgba(0, 109, 119, 0.1)',
      transform: 'scale(1.02)'
    },
    messageContainer: {
      textAlign: 'center',
      marginTop: '16px'
    },
    successMessage: {
      color: '#059669',
      fontWeight: '500',
      fontSize: '14px'
    },
    errorMessage: {
      color: '#dc2626',
      fontWeight: '500',
      fontSize: '14px'
    }
  };

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }

    fetchBooks();
    fetchSubMoods();
  }, []);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const data = await booksAPI.getAll();
      console.log('Books data received:', data);
      setBooks(data);
      setError('');
    } catch (error) {
      console.error('Error fetching books:', error);
      setError(error.message || 'Error loading books');
    } finally {
      setLoading(false);
    }
  };

  const fetchSubMoods = async () => {
    try {
      const data = await subMoodsAPI.getAll();
      setSubMoods(data);
    } catch (error) {
      console.error('Error fetching sub-moods:', error);
    }
  };



  const handleEdit = (book) => {
    console.log('Editing book:', book);
    setEditingBook(book);

    // Handle both sub_moods (array of IDs) and subMoods (Laravel relationship)
    let subMoodIds = [];
    if (book.subMoods && Array.isArray(book.subMoods)) {
      // Laravel API response format with relationship
      subMoodIds = book.subMoods.map(subMood => subMood.id);
    } else if (book.sub_moods && Array.isArray(book.sub_moods)) {
      // Direct array of IDs
      subMoodIds = book.sub_moods;
    }

    setFormData({
      title: book.title || '',
      author: book.author || '',
      description: book.description || '',
      sub_moods: subMoodIds,
      cover_image: book.cover_image || ''
    });
    setFormMode('edit');
    setShowCrudForm(true);
  };

  const handleDelete = async (bookId) => {
    if (window.confirm('Are you sure you want to delete this book?')) {
      try {
        await booksAPI.delete(bookId);
        await fetchBooks(); // Refresh the list
      } catch (error) {
        console.error('Error deleting book:', error);
        alert(error.message || 'Error deleting book');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      author: '',
      description: '',
      sub_moods: [],
      cover_image: ''
    });
    setEditingBook(null);
    setFormMode('add');
    setFormMessage('');
  };

  // CRUD form functions
  const openAddForm = () => {
    setFormMode('add');
    setFormData({
      title: '',
      author: '',
      description: '',
      sub_moods: [],
      cover_image: ''
    });
    setEditingBook(null);
    setFormMessage('');
    setShowCrudForm(true);
  };

  const openEditForm = (book) => {
    setFormMode('edit');
    setFormData({
      title: book.title,
      author: book.author,
      description: book.description,
      mood: book.mood || 'all',
      image: book.image || ''
    });
    setEditingBook(book);
    setFormMessage('');
    setShowCrudForm(true);
  };

  const closeCrudForm = () => {
    setShowCrudForm(false);
    setFormData({
      title: '',
      author: '',
      description: '',
      mood: 'all',
      image: ''
    });
    setEditingBook(null);
    setFormMessage('');
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    setFormMessage('');

    try {
      // Prepare data according to Laravel API expectations
      const bookData = {
        title: formData.title,
        author: formData.author,
        description: formData.description,
        sub_moods: formData.sub_moods // Array of sub_mood IDs
      };

      console.log('Submitting book data:', bookData);

      if (formMode === 'add') {
        const result = await booksAPI.create(bookData);
        console.log('Book created:', result);
        setFormMessage('Book added successfully!');
      } else {
        const result = await booksAPI.update(editingBook.id, bookData);
        console.log('Book updated:', result);
        setFormMessage('Book updated successfully!');
      }

      // Refresh books list
      await fetchBooks();

      // Reset form after short delay
      setTimeout(() => {
        resetForm();
        setShowCrudForm(false);
        setFormMessage('');
      }, 2000);
    } catch (error) {
      console.error('Error submitting book:', error);
      setFormMessage(error.message || 'Error submitting book');
    } finally {
      setFormLoading(false);
    }
  };



  const handleFormInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Filter books
  const filteredBooks = books.filter(book => {
    const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         book.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (selectedMood === 'all') return matchesSearch;
    
    const matchesMood = book.sub_moods && book.sub_moods.some(subMood => 
      subMood.name.toLowerCase().includes(selectedMood) ||
      subMood.meaning.toLowerCase().includes(selectedMood)
    );
    
    return matchesSearch && matchesMood;
  });

  const getMoodColor = (moodName) => {
    const mood = moodCategories.find(m => 
      moodName.toLowerCase().includes(m.id) || 
      m.name.toLowerCase().includes(moodName.toLowerCase())
    );
    return mood ? mood.color : '#6b7280';
  };

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      {/* Dashboard Sidebar */}
      <DashboardSidebar />

      {/* Main Content Area */}
      <div style={{
        paddingLeft: '256px',
        minHeight: 'calc(100vh - 140px)'
      }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto', padding: '16px 24px' }}>
          {/* Welcome Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '2fr 1fr',
            gap: '24px',
            marginBottom: '24px'
          }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h1 style={{
                  fontSize: '28px',
                  fontWeight: '700',
                  color: '#1f2937',
                  marginBottom: '8px'
                }}>Book Recommendations for {userName}!</h1>
                <p style={{ color: '#4b5563', marginTop: '4px' }}>Discover books that match your current mood and mindset</p>

                {/* Search Bar */}
                <div style={{ position: 'relative', marginTop: '16px', maxWidth: '100%' }}>
                  <input
                    type="text"
                    placeholder="Search books by title, author, or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      width: '100%',
                      maxWidth: '100%',
                      padding: '12px 40px 12px 16px',
                      border: '1px solid rgba(0, 109, 119, 0.3)',
                      borderRadius: '8px',
                      fontSize: '14px',
                      outline: 'none',
                      backgroundColor: '#f9fafb',
                      boxSizing: 'border-box'
                    }}
                  />
                  <i className="fa-solid fa-search" style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'rgba(0, 109, 119, 0.6)'
                  }}></i>
                </div>

                {/* Mood Filter Tabs */}
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px',
                  marginTop: '16px'
                }}>
                  {moodCategories.map(mood => (
                    <button
                      key={mood.id}
                      onClick={() => setSelectedMood(mood.id)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '9999px',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: '500',
                        backgroundColor: selectedMood === mood.id ? mood.color : `${mood.color}20`,
                        color: selectedMood === mood.id ? 'white' : mood.color
                      }}
                    >
                      {mood.name}
                    </button>
                  ))}
                </div>

                {/* Add New Book Button */}
                <div style={{ marginTop: '16px' }}>
                  <button
                    onClick={() => openAddForm()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: '#FF6F61',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                  >
                    <i className="fa-solid fa-plus"></i>
                    Add New Book
                  </button>
                </div>
              </div>
            </div>

            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>Book Stats</h2>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: 'rgba(0, 109, 119, 0.1)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 8px'
                    }}>
                      <i className="fa-solid fa-book" style={{ color: '#006D77', fontSize: '20px' }}></i>
                    </div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>{books.length}</div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>Total Books</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Books Display Section */}
          <div style={{ marginBottom: '32px' }}>
            {loading && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-spinner fa-spin" style={{ fontSize: '32px', color: '#FF6F61', marginBottom: '16px' }}></i>
                <p style={{ color: '#4b5563', fontSize: '16px' }}>Loading your book recommendations...</p>
              </div>
            )}

            {error && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-exclamation-triangle" style={{ fontSize: '32px', color: '#ef4444', marginBottom: '16px' }}></i>
                <p style={{ color: '#ef4444', fontSize: '16px', marginBottom: '16px' }}>{error}</p>
                <button
                  onClick={fetchBooks}
                  style={{
                    backgroundColor: '#FF6F61',
                    color: 'white',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Try Again
                </button>
              </div>
            )}

            {!loading && !error && (
              <div>
                <div style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  padding: '24px',
                  marginBottom: '24px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
                    <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1f2937' }}>
                      {filteredBooks.length} Book{filteredBooks.length !== 1 ? 's' : ''} Found
                    </h2>
                    <div style={{
                      padding: '4px 12px',
                      backgroundColor: 'rgba(0, 109, 119, 0.1)',
                      color: '#006D77',
                      borderRadius: '9999px',
                      fontSize: '14px'
                    }}>
                      {selectedMood === 'all' ? 'All Categories' : moodCategories.find(m => m.id === selectedMood)?.name}
                    </div>
                  </div>

                  {filteredBooks.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '48px' }}>
                      <i className="fa-solid fa-book-open" style={{ fontSize: '48px', color: '#d1d5db', marginBottom: '16px' }}></i>
                      <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#374151', marginBottom: '8px' }}>
                        No books found
                      </h3>
                      <p style={{ color: '#6b7280', marginBottom: '24px' }}>
                        {searchTerm ? 'Try adjusting your search terms or filters.' : 'No books match the selected mood category.'}
                      </p>
                      {searchTerm && (
                        <button
                          onClick={() => {
                            setSearchTerm('');
                            setSelectedMood('all');
                          }}
                          style={{
                            backgroundColor: '#FF6F61',
                            color: 'white',
                            padding: '12px 24px',
                            borderRadius: '8px',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500'
                          }}
                        >
                          Clear Filters
                        </button>
                      )}
                    </div>
                  ) : (
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                      gap: '24px'
                    }}>
                      {filteredBooks.map(book => (
                        <div
                          key={book.id}
                          style={{
                            backgroundColor: 'white',
                            borderRadius: '12px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            overflow: 'hidden',
                            transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                            cursor: 'pointer',
                            minHeight: '400px',
                            display: 'flex',
                            flexDirection: 'column'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-4px)';
                            e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                          }}
                        >
                          <div style={{
                            height: '160px',
                            background: book.cover_image
                              ? `url(${book.cover_image})`
                              : 'linear-gradient(135deg, #006D77 0%, #83C5BE 100%)',
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            position: 'relative',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            {!book.cover_image && (
                              <i className="fa-solid fa-book" style={{ fontSize: '48px', color: 'white', opacity: 0.8 }}></i>
                            )}
                          </div>

                          <div style={{ padding: '20px', flex: 1, display: 'flex', flexDirection: 'column' }}>
                            <h3 style={{
                              fontSize: '16px',
                              fontWeight: '600',
                              color: '#1f2937',
                              marginBottom: '8px'
                            }}>
                              {book.title}
                            </h3>

                            <p style={{
                              fontSize: '14px',
                              color: '#006D77',
                              fontWeight: '500',
                              marginBottom: '12px'
                            }}>
                              by {book.author}
                            </p>

                            <p style={{
                              fontSize: '14px',
                              color: '#6b7280',
                              lineHeight: '1.5',
                              marginBottom: '16px',
                              display: '-webkit-box',
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden'
                            }}>
                              {book.description}
                            </p>

                            {book.sub_moods && book.sub_moods.length > 0 && (
                              <div style={{ marginBottom: '16px' }}>
                                <div style={{
                                  fontSize: '12px',
                                  fontWeight: '600',
                                  color: '#374151',
                                  marginBottom: '8px'
                                }}>
                                  Recommended for:
                                </div>
                                <div style={{
                                  display: 'flex',
                                  flexWrap: 'wrap',
                                  gap: '6px'
                                }}>
                                  {book.sub_moods.slice(0, 2).map(subMood => (
                                    <span
                                      key={subMood.id}
                                      style={{
                                        padding: '4px 8px',
                                        backgroundColor: `${getMoodColor(subMood.name)}20`,
                                        color: getMoodColor(subMood.name),
                                        borderRadius: '12px',
                                        fontSize: '11px',
                                        fontWeight: '500'
                                      }}
                                    >
                                      {subMood.name}
                                    </span>
                                  ))}
                                  {book.sub_moods.length > 2 && (
                                    <span style={{
                                      padding: '4px 8px',
                                      backgroundColor: '#f3f4f6',
                                      color: '#6b7280',
                                      borderRadius: '12px',
                                      fontSize: '11px',
                                      fontWeight: '500'
                                    }}>
                                      +{book.sub_moods.length - 2} more
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}

                            <div style={{ display: 'flex', gap: '8px', marginTop: '16px' }}>
                              <button
                                onClick={() => openEditForm(book)}
                                style={{
                                  flex: 1,
                                  backgroundColor: '#006D77',
                                  color: 'white',
                                  padding: '10px 16px',
                                  borderRadius: '8px',
                                  border: 'none',
                                  cursor: 'pointer',
                                  fontSize: '14px',
                                  fontWeight: '500',
                                  transition: 'background-color 0.2s ease'
                                }}
                                onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.9)'}
                                onMouseLeave={(e) => e.target.style.backgroundColor = '#006D77'}
                              >
                                <i className="fa-solid fa-edit" style={{ marginRight: '6px' }}></i>
                                Edit
                              </button>
                              <button
                                onClick={() => handleDelete(book.id)}
                                style={{
                                  flex: 1,
                                  backgroundColor: '#dc2626',
                                  color: 'white',
                                  padding: '10px 16px',
                                  borderRadius: '8px',
                                  border: 'none',
                                  cursor: 'pointer',
                                  fontSize: '14px',
                                  fontWeight: '500',
                                  transition: 'background-color 0.2s ease'
                                }}
                                onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(220, 38, 38, 0.9)'}
                                onMouseLeave={(e) => e.target.style.backgroundColor = '#dc2626'}
                              >
                                <i className="fa-solid fa-trash" style={{ marginRight: '6px' }}></i>
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CRUD Form Modal */}
      {showCrudForm && (
        <div style={formStyles.overlay} onClick={(e) => e.target === e.currentTarget && closeCrudForm()}>
          <div style={formStyles.formCard}>
            <h2 style={formStyles.title}>
              {formMode === 'add' ? 'Add New Book' : 'Edit Book'}
            </h2>

            <form onSubmit={handleFormSubmit} style={formStyles.form}>
              {/* Title Field */}
              <div style={formStyles.fieldContainer}>
                <label style={formStyles.label}>Book Title</label>
                <div
                  style={{
                    ...formStyles.inputContainer,
                    ...(titleHovered ? formStyles.inputContainerHover : {})
                  }}
                  onMouseEnter={() => setTitleHovered(true)}
                  onMouseLeave={() => setTitleHovered(false)}
                >
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleFormInputChange('title', e.target.value)}
                    onFocus={() => setTitleFocused(true)}
                    onBlur={() => setTitleFocused(false)}
                    style={{
                      ...formStyles.input,
                      ...(titleFocused ? formStyles.inputFocus : {})
                    }}
                    placeholder="Enter book title"
                    required
                  />
                </div>
              </div>

              {/* Author Field */}
              <div style={formStyles.fieldContainer}>
                <label style={formStyles.label}>Author</label>
                <div
                  style={{
                    ...formStyles.inputContainer,
                    ...(authorHovered ? formStyles.inputContainerHover : {})
                  }}
                  onMouseEnter={() => setAuthorHovered(true)}
                  onMouseLeave={() => setAuthorHovered(false)}
                >
                  <input
                    type="text"
                    value={formData.author}
                    onChange={(e) => handleFormInputChange('author', e.target.value)}
                    onFocus={() => setAuthorFocused(true)}
                    onBlur={() => setAuthorFocused(false)}
                    style={{
                      ...formStyles.input,
                      ...(authorFocused ? formStyles.inputFocus : {})
                    }}
                    placeholder="Enter author name"
                    required
                  />
                </div>
              </div>

              {/* Description Field */}
              <div style={formStyles.fieldContainer}>
                <label style={formStyles.label}>Description</label>
                <div
                  style={{
                    ...formStyles.inputContainer,
                    ...(descriptionHovered ? formStyles.inputContainerHover : {})
                  }}
                  onMouseEnter={() => setDescriptionHovered(true)}
                  onMouseLeave={() => setDescriptionHovered(false)}
                >
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleFormInputChange('description', e.target.value)}
                    onFocus={() => setDescriptionFocused(true)}
                    onBlur={() => setDescriptionFocused(false)}
                    style={{
                      ...formStyles.textarea,
                      ...(descriptionFocused ? formStyles.inputFocus : {})
                    }}
                    placeholder="Enter book description"
                    required
                  />
                </div>
              </div>

              {/* Sub Moods Field */}
              <div style={formStyles.fieldContainer}>
                <label style={formStyles.label}>Sub Moods</label>
                <div
                  style={{
                    ...formStyles.inputContainer,
                    ...(subMoodsHovered ? formStyles.inputContainerHover : {}),
                    minHeight: '120px',
                    padding: '12px'
                  }}
                  onMouseEnter={() => setSubMoodsHovered(true)}
                  onMouseLeave={() => setSubMoodsHovered(false)}
                >
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                    gap: '8px',
                    maxHeight: '150px',
                    overflowY: 'auto'
                  }}>
                    {subMoods.map(subMood => (
                      <label
                        key={subMood.id}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          padding: '8px',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          backgroundColor: formData.sub_moods.includes(subMood.id) ? 'rgba(255, 111, 97, 0.1)' : 'transparent',
                          border: formData.sub_moods.includes(subMood.id) ? '1px solid #FF6F61' : '1px solid transparent',
                          transition: 'all 0.2s ease'
                        }}
                      >
                        <input
                          type="checkbox"
                          checked={formData.sub_moods.includes(subMood.id)}
                          onChange={(e) => {
                            const newSubMoods = e.target.checked
                              ? [...formData.sub_moods, subMood.id]
                              : formData.sub_moods.filter(id => id !== subMood.id);
                            handleFormInputChange('sub_moods', newSubMoods);
                          }}
                          style={{
                            accentColor: '#FF6F61',
                            width: '16px',
                            height: '16px'
                          }}
                        />
                        <div>
                          <div style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            color: '#374151'
                          }}>
                            {subMood.name}
                          </div>
                          {subMood.meaning && (
                            <div style={{
                              fontSize: '12px',
                              color: '#6b7280',
                              marginTop: '2px'
                            }}>
                              {subMood.meaning}
                            </div>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>
                  {subMoods.length === 0 && (
                    <div style={{
                      textAlign: 'center',
                      color: '#6b7280',
                      fontSize: '14px',
                      padding: '20px'
                    }}>
                      No sub moods available. Please add some moods first.
                    </div>
                  )}
                </div>
              </div>

              {/* Image URL Field */}
              <div style={formStyles.fieldContainer}>
                <label style={formStyles.label}>Cover Image URL (Optional)</label>
                <div
                  style={{
                    ...formStyles.inputContainer,
                    ...(imageHovered ? formStyles.inputContainerHover : {})
                  }}
                  onMouseEnter={() => setImageHovered(true)}
                  onMouseLeave={() => setImageHovered(false)}
                >
                  <input
                    type="url"
                    value={formData.cover_image}
                    onChange={(e) => handleFormInputChange('cover_image', e.target.value)}
                    onFocus={() => setImageFocused(true)}
                    onBlur={() => setImageFocused(false)}
                    style={{
                      ...formStyles.input,
                      ...(imageFocused ? formStyles.inputFocus : {})
                    }}
                    placeholder="Enter image URL"
                  />
                </div>
              </div>

              {/* Form Buttons */}
              <div style={formStyles.buttonContainer}>
                <button
                  type="button"
                  onClick={closeCrudForm}
                  style={{
                    ...formStyles.cancelButton,
                    ...(cancelHovered ? formStyles.cancelButtonHover : {})
                  }}
                  onMouseEnter={() => setCancelHovered(true)}
                  onMouseLeave={() => setCancelHovered(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  style={{
                    ...formStyles.submitButton,
                    ...(submitHovered && !formLoading ? formStyles.submitButtonHover : {}),
                    ...(formLoading ? { opacity: 0.5, cursor: 'not-allowed' } : {})
                  }}
                  onMouseEnter={() => setSubmitHovered(true)}
                  onMouseLeave={() => setSubmitHovered(false)}
                >
                  {formLoading ? (
                    <>
                      <i className="fa-solid fa-spinner fa-spin"></i>
                      {formMode === 'add' ? 'Adding...' : 'Updating...'}
                    </>
                  ) : (
                    <>
                      <i className={`fa-solid ${formMode === 'add' ? 'fa-plus' : 'fa-save'}`}></i>
                      {formMode === 'add' ? 'Add Book' : 'Update Book'}
                    </>
                  )}
                </button>
              </div>

              {/* Form Message */}
              {formMessage && (
                <div style={formStyles.messageContainer}>
                  <div style={formMessage.includes('successfully') ? formStyles.successMessage : formStyles.errorMessage}>
                    {formMessage}
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default Books;
