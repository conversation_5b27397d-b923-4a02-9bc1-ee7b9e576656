import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

function UserDashboard() {
  const navigate = useNavigate();
  const [showSidebar, setShowSidebar] = useState(false);
  const [userName, setUserName] = useState('User');

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    }
  }, []);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      <style>
        {`
          .hover\\:bg-gray-100:hover {
            background-color: #f3f4f6;
          }
          @media (min-width: 768px) {
            .md\\:translate-x-0 {
              transform: translateX(0) !important;
            }
          }
        `}
      </style>

      {/* Left Sidebar Navigation */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        bottom: 0,
        width: '256px',
        backgroundColor: 'white',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        transform: showSidebar ? 'translateX(0)' : 'translateX(-100%)',
        transition: 'transform 0.3s ease-in-out',
        zIndex: 40,
        ...(window.innerWidth >= 768 ? { transform: 'translateX(0)' } : {})
      }}>
        <div style={{ padding: '16px', borderBottom: '1px solid #e5e7eb' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#FF6F61',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ color: 'white', fontWeight: 'bold', fontSize: '16px' }}>LB</span>
              </div>
              <span style={{ marginLeft: '12px', fontWeight: '600', color: '#1f2937' }}>LiberateBites</span>
            </div>
            <button
              onClick={toggleSidebar}
              style={{
                display: window.innerWidth >= 768 ? 'none' : 'block',
                padding: '4px',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              <i className="fa-solid fa-xmark" style={{ fontSize: '20px', color: '#6b7280' }}></i>
            </button>
          </div>
        </div>

        <div style={{ flex: 1, padding: '16px 0' }}>
          <nav style={{ display: 'flex', flexDirection: 'column', gap: '4px', padding: '0 16px' }}>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer',
              backgroundColor: '#f3f4f6'
            }} className="hover:bg-gray-100">
              <i className="fa-solid fa-home" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>Dashboard</span>
            </span>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer'
            }} className="hover:bg-gray-100" onClick={() => navigate('/quiz')}>
              <i className="fa-solid fa-brain" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>Mood Quiz</span>
            </span>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer'
            }} className="hover:bg-gray-100" onClick={() => navigate('/food-menu')}>
              <i className="fa-solid fa-utensils" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>Food Menu</span>
            </span>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer'
            }} className="hover:bg-gray-100">
              <i className="fa-solid fa-shopping-bag" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>My Orders</span>
            </span>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer'
            }} className="hover:bg-gray-100">
              <i className="fa-solid fa-heart" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>Mood History</span>
            </span>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer'
            }} className="hover:bg-gray-100">
              <i className="fa-solid fa-book" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>Book Recommendations</span>
            </span>
            <span style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px 16px',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer'
            }} className="hover:bg-gray-100">
              <i className="fa-solid fa-user" style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>Profile</span>
            </span>
          </nav>
        </div>

        <div style={{ padding: '16px', borderTop: '1px solid #e5e7eb' }}>
          <div style={{
            background: 'linear-gradient(to right, #006D77, rgba(255, 111, 97, 0.6))',
            borderRadius: '12px',
            padding: '16px',
            color: 'white',
            marginBottom: '16px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <i className="fa-solid fa-fire-flame-curved" style={{ fontSize: '20px' }}></i>
              <span style={{ marginLeft: '8px', fontWeight: '600' }}>Mood Streak</span>
            </div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '4px' }}>7 Days</div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>Keep tracking your moods to grow your streak!</div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {showSidebar && window.innerWidth < 768 && (
        <div
          style={{
            position: 'fixed',
            inset: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 30
          }}
          onClick={toggleSidebar}
        />
      )}

      {/* Mobile Header */}
      <div style={{
        display: window.innerWidth >= 768 ? 'none' : 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '16px 24px',
        backgroundColor: 'white',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        position: 'sticky',
        top: '70px',
        zIndex: 20
      }}>
        <h1 style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937' }}>Dashboard</h1>
        <button
          onClick={toggleSidebar}
          style={{
            padding: '8px',
            backgroundColor: 'transparent',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          <i className="fa-solid fa-bars" style={{ fontSize: '20px', color: '#6b7280' }}></i>
        </button>
      </div>

      {/* Main Content Area */}
      <div style={{
        paddingTop: '0px',
        paddingLeft: window.innerWidth >= 768 ? '256px' : '0',
        minHeight: 'calc(100vh - 140px)'
      }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto', padding: '16px 24px' }}>
          {/* Welcome Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth >= 768 ? '2fr 1fr' : '1fr',
            gap: '24px',
            marginBottom: '24px'
          }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h1 style={{
                  fontSize: window.innerWidth >= 768 ? '30px' : '24px',
                  fontWeight: 'bold',
                  color: '#1f2937'
                }}>Welcome back, {userName}!</h1>
                <p style={{ color: '#4b5563', marginTop: '4px' }}>Ready to discover your perfect meal match?</p>

                <div style={{ marginTop: '24px' }}>
                  <h2 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>
                    Quick Actions
                  </h2>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
                    <button
                      onClick={() => navigate('/quiz')}
                      style={{
                        padding: '16px',
                        backgroundColor: '#FF6F61',
                        color: 'white',
                        borderRadius: '12px',
                        border: 'none',
                        cursor: 'pointer',
                        textAlign: 'left',
                        transition: 'all 0.2s ease',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)';
                        e.target.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = '#FF6F61';
                        e.target.style.transform = 'scale(1)';
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                        <i className="fa-solid fa-brain" style={{ fontSize: '20px', marginRight: '12px' }}></i>
                        <span style={{ fontWeight: '600' }}>Take Mood Quiz</span>
                      </div>
                      <p style={{ fontSize: '14px', opacity: 0.9, margin: 0 }}>
                        Discover food that matches your current mood
                      </p>
                    </button>

                    <button
                      onClick={() => navigate('/food-menu')}
                      style={{
                        padding: '16px',
                        backgroundColor: '#006D77',
                        color: 'white',
                        borderRadius: '12px',
                        border: 'none',
                        cursor: 'pointer',
                        textAlign: 'left',
                        transition: 'all 0.2s ease',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.9)';
                        e.target.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = '#006D77';
                        e.target.style.transform = 'scale(1)';
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                        <i className="fa-solid fa-utensils" style={{ fontSize: '20px', marginRight: '12px' }}></i>
                        <span style={{ fontWeight: '600' }}>Browse Menu</span>
                      </div>
                      <p style={{ fontSize: '14px', opacity: 0.9, margin: 0 }}>
                        Explore our full menu and place orders
                      </p>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats Card */}
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>
                  Your Journey
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{
                    padding: '16px',
                    backgroundColor: 'rgba(255, 111, 97, 0.1)',
                    borderRadius: '12px',
                    border: '1px solid rgba(255, 111, 97, 0.2)'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <div style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'rgba(255, 111, 97, 0.2)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <i className="fa-solid fa-brain" style={{ color: '#FF6F61' }}></i>
                      </div>
                      <span style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>12</span>
                    </div>
                    <div style={{ fontSize: '14px', color: '#4b5563' }}>Quizzes Completed</div>
                  </div>

                  <div style={{
                    padding: '16px',
                    backgroundColor: 'rgba(0, 109, 119, 0.1)',
                    borderRadius: '12px',
                    border: '1px solid rgba(0, 109, 119, 0.2)'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <div style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 109, 119, 0.2)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <i className="fa-solid fa-shopping-bag" style={{ color: '#006D77' }}></i>
                      </div>
                      <span style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>8</span>
                    </div>
                    <div style={{ fontSize: '14px', color: '#4b5563' }}>Orders Placed</div>
                  </div>

                  <div style={{
                    padding: '16px',
                    backgroundColor: 'rgba(245, 232, 199, 0.5)',
                    borderRadius: '12px',
                    border: '1px solid rgba(245, 232, 199, 0.8)'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <div style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'rgba(245, 232, 199, 0.8)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <i className="fa-solid fa-heart" style={{ color: '#006D77' }}></i>
                      </div>
                      <span style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>15</span>
                    </div>
                    <div style={{ fontSize: '14px', color: '#4b5563' }}>Moods Tracked</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth >= 768 ? '1fr 1fr' : '1fr',
            gap: '24px',
            marginBottom: '24px'
          }}>
            {/* Recent Orders */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
              padding: '24px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
                <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', margin: 0 }}>Recent Orders</h3>
                <button style={{
                  padding: '6px 12px',
                  backgroundColor: 'transparent',
                  color: '#FF6F61',
                  border: '1px solid #FF6F61',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  View All
                </button>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <div style={{
                  padding: '12px',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                    <span style={{ fontWeight: '500', color: '#1f2937' }}>Rajma-Chawal Bowl</span>
                    <span style={{ fontSize: '12px', color: '#10b981', fontWeight: '500' }}>Delivered</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>Yesterday, 2:30 PM</span>
                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#1f2937' }}>₹99</span>
                  </div>
                </div>

                <div style={{
                  padding: '12px',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                    <span style={{ fontWeight: '500', color: '#1f2937' }}>Chocolate Brownie</span>
                    <span style={{ fontSize: '12px', color: '#f59e0b', fontWeight: '500' }}>Preparing</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>Today, 1:15 PM</span>
                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#1f2937' }}>₹99</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Mood Insights */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
              padding: '24px'
            }}>
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>
                Recent Mood Insights
              </h3>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <div style={{
                  padding: '12px',
                  backgroundColor: 'rgba(255, 111, 97, 0.05)',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 111, 97, 0.1)'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 111, 97, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '12px'
                    }}>
                      <i className="fa-solid fa-brain" style={{ color: '#FF6F61', fontSize: '14px' }}></i>
                    </div>
                    <div>
                      <div style={{ fontWeight: '500', color: '#1f2937', fontSize: '14px' }}>Overthinking</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>Yesterday</div>
                    </div>
                  </div>
                  <div style={{ fontSize: '12px', color: '#4b5563', marginLeft: '44px' }}>
                    Recommended: "The Power of Now" • Rajma-Chawal Bowl
                  </div>
                </div>

                <div style={{
                  padding: '12px',
                  backgroundColor: 'rgba(0, 109, 119, 0.05)',
                  borderRadius: '8px',
                  border: '1px solid rgba(0, 109, 119, 0.1)'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0, 109, 119, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '12px'
                    }}>
                      <i className="fa-solid fa-heart" style={{ color: '#006D77', fontSize: '14px' }}></i>
                    </div>
                    <div>
                      <div style={{ fontWeight: '500', color: '#1f2937', fontSize: '14px' }}>Energetic</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>2 days ago</div>
                    </div>
                  </div>
                  <div style={{ fontSize: '12px', color: '#4b5563', marginLeft: '44px' }}>
                    Recommended: "Atomic Habits" • Veg Hakka Noodles
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <div style={{
        position: 'fixed',
        right: '24px',
        bottom: '24px',
        zIndex: 30
      }}>
        <button style={{
          width: '56px',
          height: '56px',
          backgroundColor: '#FF6F61',
          color: 'white',
          borderRadius: '50%',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          border: 'none',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'background-color 0.3s ease'
        }} onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
           onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
           onClick={() => navigate('/quiz')}>
          <i className="fa-solid fa-brain" style={{ fontSize: '20px' }}></i>
        </button>
      </div>
    </div>
  );
}

export default UserDashboard;
