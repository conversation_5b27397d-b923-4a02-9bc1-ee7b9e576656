<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>::-webkit-scrollbar { display: none;}</style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "coral": "#FF6F61",
        "teal": "#006D77",
        "peach": "#FFDAB9",
        "beige": "#F5E8C7"
      },
      "fontFamily": {
        "inter": [
          "Inter",
          "sans-serif"
        ],
        "sans": [
          "Inter",
          "sans-serif"
        ]
      },
      "borderRadius": {
        "4xl": "16px"
      },
      "animation": {
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "float": "float 3s ease-in-out infinite"
      },
      "keyframes": {
        "float": {
          "0%, 100%": {
            "transform": "translateY(0)"
          },
          "50%": {
            "transform": "translateY(-10px)"
          }
        }
      }
    }
  }
};</script>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style>
</head>
<body class="font-inter bg-gray-50">
    
    <!-- Header Section -->
    <header id="header" class="w-full bg-gradient-to-r from-beige to-beige/80 shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="h-10 w-10 rounded-full bg-coral flex items-center justify-center text-white mr-3">
                        <i class="fa-solid fa-utensils"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-800">LiberateBites</h1>
                </div>
                
                <div class="flex items-center space-x-6">
                    <div class="hidden md:flex items-center">
                        <span class="text-sm text-gray-600 mr-2">
                            <i class="fa-solid fa-fire text-amber-500 mr-1"></i>
                            <span>5 day streak</span>
                        </span>
                    </div>
                    
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-coral transition">
                            <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-1.jpg" alt="User" class="w-8 h-8 rounded-full border-2 border-coral">
                            <span class="hidden md:inline-block">Sarah</span>
                            <i class="fa-solid fa-chevron-down text-xs"></i>
                        </button>
                        
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-10 hidden group-hover:block transition-all">
                            <span class="block px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer">
                                <i class="fa-solid fa-user mr-2 text-coral"></i> Profile
                            </span>
                            <span class="block px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer">
                                <i class="fa-solid fa-gear mr-2 text-coral"></i> Settings
                            </span>
                            <span class="block px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer">
                                <i class="fa-solid fa-clock-rotate-left mr-2 text-coral"></i> History
                            </span>
                            <div class="border-t border-gray-100 my-1"></div>
                            <span class="block px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer">
                                <i class="fa-solid fa-right-from-bracket mr-2 text-coral"></i> Logout
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <!-- Hero Welcome Section -->
        <section id="welcome-section" class="mb-8">
            <div class="bg-gradient-to-r from-white to-peach/50 rounded-4xl p-8 shadow-sm">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800">Welcome back, Sarah!</h1>
                        <p class="text-lg text-gray-600 mt-1">Ready to discover your perfect meal match today?</p>
                        <p class="text-coral font-medium mt-2">
                            <i class="fa-solid fa-sun mr-1"></i> Good afternoon! How are you feeling?
                        </p>
                    </div>
                    <div class="mt-4 md:mt-0 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm flex items-center">
                        <i class="fa-solid fa-medal text-amber-500 mr-2"></i>
                        <span class="text-sm font-medium">5 days of mindful eating</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Choice Section -->
        <section id="main-choices" class="mb-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Card - Mood Quiz -->
                <div id="mood-quiz-card" class="bg-white rounded-4xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                    <div class="p-6 md:p-8">
                        <div class="flex flex-col md:flex-row items-center mb-6">
                            <div class="w-32 h-32 relative mb-4 md:mb-0 md:mr-6">
                                <img class="w-full h-full object-contain animate-float" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/597fca130f-890ed68c096d4a9dc8be.png" alt="person meditating with colorful mood bubbles floating around, minimalist illustration, peaceful">
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-800 mb-2">Discover Through Your Mood</h2>
                                <p class="text-gray-600 mb-4">Take our 5-minute personalized assessment to find food that matches your emotional state right now</p>
                                <div class="space-y-2 mb-6">
                                    <div class="flex items-center">
                                        <i class="fa-solid fa-sparkles text-amber-400 mr-2"></i>
                                        <span class="text-sm text-gray-700">100+ emotional states covered</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fa-solid fa-book text-teal mr-2"></i>
                                        <span class="text-sm text-gray-700">Get a matching book recommendation</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fa-solid fa-bullseye text-coral mr-2"></i>
                                        <span class="text-sm text-gray-700">Scientifically personalized results</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col md:flex-row justify-between items-center">
                            <button class="w-full md:w-auto bg-coral hover:bg-coral/90 text-white font-semibold py-3 px-8 rounded-full shadow-lg hover:shadow-coral/30 transition-all duration-300 animate-pulse-slow">
                                Start Mood Quiz
                            </button>
                            <div class="mt-4 md:mt-0 text-sm text-gray-500 flex items-center">
                                <i class="fa-solid fa-clock-rotate-left mr-1"></i>
                                Last result: Comfort Food for Relaxation
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Card - Direct Menu -->
                <div id="menu-card" class="bg-white rounded-4xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                    <div class="p-6 md:p-8">
                        <div class="flex flex-col md:flex-row items-center mb-6">
                            <div class="w-32 h-32 relative mb-4 md:mb-0 md:mr-6 rounded-xl overflow-hidden">
                                <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/4fbfadd89a-1d8df480d55f4db785c2.png" alt="beautiful spread of Indian vegetarian comfort food with soft lighting, rajma chawal, paneer, thali">
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-800 mb-2">Browse Our Menu</h2>
                                <p class="text-gray-600 mb-4">Explore our curated collection of vegetarian comfort foods, organized by mood and craving type</p>
                                <div class="space-y-2 mb-6">
                                    <div class="flex items-center">
                                        <i class="fa-solid fa-seedling text-green-—is500 mr-2"></i>
                                        <span class="text-sm text-gray-700">100% vegetarian cuisine</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fa-solid fa-heart text-red-500 mr-2"></i>
                                        <span class="text-sm text-gray-700">Comfort food specialists</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fa-solid fa-tag text-blue-500 mr-2"></i>
                                        <span class="text-sm text-gray-700">Mood-tagged recipes</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col md:flex-row justify-between items-center">
                            <button class="w-full md:w-auto bg-white border-2 border-coral text-coral hover:bg-coral/5 font-semibold py-3 px-8 rounded-full shadow-md transition-all duration-300">
                                Explore Menu
                            </button>
                            <div class="mt-4 md:mt-0 text-sm text-gray-500 flex items-center">
                                <i class="fa-solid fa-star text-amber-400 mr-1"></i>
                                Popular today: Rajma-Chawal Bowl
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions Bar -->
        <section id="quick-actions" class="mb-10">
            <div class="bg-white rounded-4xl shadow-sm p-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button class="flex items-center justify-center space-x-3 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors">
                        <div class="w-10 h-10 bg-peach rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-utensils text-coral"></i>
                        </div>
                        <span class="font-medium text-gray-700">Repeat Last Order</span>
                    </button>
                    
                    <button class="flex items-center justify-center space-x-3 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors">
                        <div class="w-10 h-10 bg-peach rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-dice text-coral"></i>
                        </div>
                        <span class="font-medium text-gray-700">Feeling Lucky</span>
                    </button>
                    
                    <button class="flex items-center justify-center space-x-3 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors">
                        <div class="w-10 h-10 bg-peach rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-clock-rotate-left text-coral"></i>
                        </div>
                        <span class="font-medium text-gray-700">View History</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- Mood Insights Section -->
        <section id="mood-insights" class="mb-10">
            <div class="bg-teal rounded-4xl shadow-md p-8">
                <h2 class="text-2xl font-bold text-white mb-6">Your Journey So Far</h2>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                        <div class="w-12 h-12 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-2">
                            <i class="fa-solid fa-clipboard-question text-white text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white">12</h3>
                        <p class="text-white/80 text-sm">Quizzes Taken</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                        <div class="w-12 h-12 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-2">
                            <i class="fa-solid fa-face-smile text-white text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white">8</h3>
                        <p class="text-white/80 text-sm">Different Moods</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                        <div class="w-12 h-12 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-2">
                            <i class="fa-solid fa-plate-wheat text-white text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white">24</h3>
                        <p class="text-white/80 text-sm">Meals Matched</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                        <div class="w-12 h-12 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-2">
                            <i class="fa-solid fa-book text-white text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white">5</h3>
                        <p class="text-white/80 text-sm">Books Discovered</p>
                    </div>
                </div>
                
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                    <div class="flex items-center">
                        <i class="fa-solid fa-calendar-day text-white mr-3"></i>
                        <p class="text-white">
                            <span class="font-medium">Yesterday:</span> Overthinking → Tomato Soup + 'The Power of Now'
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recommendation Preview -->
        <section id="trending-matches">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Trending Mood Matches Today</h2>
            
            <div class="flex overflow-x-auto space-x-4 pb-4">
                <div class="flex-shrink-0 w-64 bg-white rounded-4xl shadow-md overflow-hidden">
                    <div class="h-32 relative">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/b155e559f0-671bc6cf4e61c2f4ae72.png" alt="rajma chawal bowl indian comfort food">
                        <div class="absolute top-3 left-3 bg-white/80 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                            <i class="fa-solid fa-brain text-red-500 mr-1"></i>
                            <span class="text-xs font-medium">Feeling Stressed</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-2">Rajma-Chawal Bowl</h3>
                        <button class="w-full bg-coral/10 hover:bg-coral/20 text-coral font-medium py-2 rounded-lg transition-colors text-sm">
                            Try This Match
                        </button>
                    </div>
                </div>
                
                <div class="flex-shrink-0 w-64 bg-white rounded-4xl shadow-md overflow-hidden">
                    <div class="h-32 relative">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/79a4a0abac-55884704315b62ee6f30.png" alt="paneer paratha indian food">
                        <div class="absolute top-3 left-3 bg-white/80 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                            <i class="fa-solid fa-bolt text-yellow-500 mr-1"></i>
                            <span class="text-xs font-medium">Need Energy</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-2">Paneer Paratha</h3>
                        <button class="w-full bg-coral/10 hover:bg-coral/20 text-coral font-medium py-2 rounded-lg transition-colors text-sm">
                            Try This Match
                        </button>
                    </div>
                </div>
                
                <div class="flex-shrink-0 w-64 bg-white rounded-4xl shadow-md overflow-hidden">
                    <div class="h-32 relative">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/29ac1ddad8-f8891af7d8badfe132ef.png" alt="masala chai indian tea with spices">
                        <div class="absolute top-3 left-3 bg-white/80 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                            <i class="fa-solid fa-hand-holding-heart text-purple-500 mr-1"></i>
                            <span class="text-xs font-medium">Comfort Seeking</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-2">Masala Milk Tea</h3>
                        <button class="w-full bg-coral/10 hover:bg-coral/20 text-coral font-medium py-2 rounded-lg transition-colors text-sm">
                            Try This Match
                        </button>
                    </div>
                </div>
                
                <div class="flex-shrink-0 w-64 bg-white rounded-4xl shadow-md overflow-hidden">
                    <div class="h-32 relative">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/a25e7285b0-a2e86672145868da2945.png" alt="dal tadka indian lentil soup">
                        <div class="absolute top-3 left-3 bg-white/80 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                            <i class="fa-solid fa-cloud-rain text-blue-500 mr-1"></i>
                            <span class="text-xs font-medium">Feeling Gloomy</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-2">Dal Tadka</h3>
                        <button class="w-full bg-coral/10 hover:bg-coral/20 text-coral font-medium py-2 rounded-lg transition-colors text-sm">
                            Try This Match
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer id="footer" class="bg-coral mt-16">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-white text-center md:text-left mb-4 md:mb-0">
                    © 2024 Liberate Bites. Made with <i class="fa-solid fa-heart"></i> for mindful food lovers.
                </p>
                <div class="flex space-x-6">
                    <span class="text-white hover:text-white/80 transition-colors cursor-pointer">Help</span>
                    <span class="text-white hover:text-white/80 transition-colors cursor-pointer">About</span>
                    <span class="text-white hover:text-white/80 transition-colors cursor-pointer">Contact</span>
                </div>
            </div>
        </div>
    </footer>

</body>
</html>