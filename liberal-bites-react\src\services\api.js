const API_BASE_URL = 'http://168.231.122.170/cstm-api/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }
  return response.json();
};

// Books API
export const booksAPI = {
  // Get all books
  getAll: async () => {
    const response = await fetch(`${API_BASE_URL}/books`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Get single book
  getById: async (id) => {
    const response = await fetch(`${API_BASE_URL}/books/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Create new book
  create: async (bookData) => {
    const response = await fetch(`${API_BASE_URL}/books`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(bookData),
    });
    return handleResponse(response);
  },

  // Update book
  update: async (id, bookData) => {
    const response = await fetch(`${API_BASE_URL}/books/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(bookData),
    });
    return handleResponse(response);
  },

  // Delete book
  delete: async (id) => {
    const response = await fetch(`${API_BASE_URL}/books/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Moods API
export const moodsAPI = {
  // Get all moods with sub-moods
  getAll: async () => {
    const response = await fetch(`${API_BASE_URL}/moods`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Get single mood
  getById: async (id) => {
    const response = await fetch(`${API_BASE_URL}/moods/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Create new mood
  create: async (moodData) => {
    const response = await fetch(`${API_BASE_URL}/moods`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(moodData),
    });
    return handleResponse(response);
  },

  // Update mood
  update: async (id, moodData) => {
    const response = await fetch(`${API_BASE_URL}/moods/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(moodData),
    });
    return handleResponse(response);
  },

  // Delete mood
  delete: async (id) => {
    const response = await fetch(`${API_BASE_URL}/moods/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Sub-Moods API
export const subMoodsAPI = {
  // Get all sub-moods
  getAll: async () => {
    const response = await fetch(`${API_BASE_URL}/sub-moods`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Create new sub-mood
  create: async (subMoodData) => {
    const response = await fetch(`${API_BASE_URL}/sub-moods`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(subMoodData),
    });
    return handleResponse(response);
  },

  // Update sub-mood
  update: async (id, subMoodData) => {
    const response = await fetch(`${API_BASE_URL}/sub-moods/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(subMoodData),
    });
    return handleResponse(response);
  },

  // Delete sub-mood
  delete: async (id) => {
    const response = await fetch(`${API_BASE_URL}/sub-moods/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// MCQ/Quiz API
export const quizzesAPI = {
  // Get all quiz questions with options
  getAll: async () => {
    const response = await fetch(`${API_BASE_URL}/mcqs`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Get single quiz question
  getById: async (id) => {
    const response = await fetch(`${API_BASE_URL}/mcqs/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Create new quiz question
  create: async (quizData) => {
    const response = await fetch(`${API_BASE_URL}/mcqs`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(quizData),
    });
    return handleResponse(response);
  },

  // Update quiz question
  update: async (id, quizData) => {
    const response = await fetch(`${API_BASE_URL}/mcqs/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(quizData),
    });
    return handleResponse(response);
  },

  // Delete quiz question
  delete: async (id) => {
    const response = await fetch(`${API_BASE_URL}/mcqs/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Delete quiz option
  deleteOption: async (optionId) => {
    const response = await fetch(`${API_BASE_URL}/mcqs/options/${optionId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Food Items API
export const foodItemsAPI = {
  // Get all food items
  getAll: async () => {
    const response = await fetch(`${API_BASE_URL}/food-items`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Get single food item
  getById: async (id) => {
    const response = await fetch(`${API_BASE_URL}/food-items/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Create new food item
  create: async (foodData) => {
    const response = await fetch(`${API_BASE_URL}/food-items`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(foodData),
    });
    return handleResponse(response);
  },

  // Update food item
  update: async (id, foodData) => {
    const response = await fetch(`${API_BASE_URL}/food-items/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(foodData),
    });
    return handleResponse(response);
  },

  // Delete food item
  delete: async (id) => {
    const response = await fetch(`${API_BASE_URL}/food-items/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Auth API
export const authAPI = {
  // Login
  login: async (credentials) => {
    const response = await fetch(`${API_BASE_URL}/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });
    return handleResponse(response);
  },

  // Register
  register: async (userData) => {
    const response = await fetch(`${API_BASE_URL}/admin/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });
    return handleResponse(response);
  },

  // Get current user
  me: async () => {
    const response = await fetch(`${API_BASE_URL}/admin/me`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Logout
  logout: async () => {
    const response = await fetch(`${API_BASE_URL}/admin/logout`, {
      method: 'POST',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};
