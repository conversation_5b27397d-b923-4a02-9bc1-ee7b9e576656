import { Routes, Route, Navigate } from 'react-router-dom'
import Login from './pages/Login'
import Register from './pages/Register'
import MainMenu from './pages/mainMenu'
import QuizPage from './pages/quizPage'
import FoodMenu from './pages/FoodMenu'
import Menu from './pages/dashboard/menu'
import Moods from './pages/dashboard/Moods'
import Books from './pages/dashboard/Books'
import Quizzes from './pages/dashboard/Quizzes'
import Foods from './pages/Foods'
import AdminDashboard from './pages/dashboard/AdminDashboard'
import UserDashboard from './pages/UserDashboard'
import DashboardLayout from './components/DashboardLayout'
import ProtectedRoute from './utils/ProtectedRoute'

function App() {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/main-menu" element={<ProtectedRoute><MainMenu /></ProtectedRoute>} />
      <Route path="/quiz" element={<ProtectedRoute><QuizPage /></ProtectedRoute>} />
      <Route path="/food-menu" element={<ProtectedRoute><FoodMenu /></ProtectedRoute>} />
      <Route path="/admin-dashboard" element={<ProtectedRoute requireAdmin={true}><DashboardLayout /></ProtectedRoute>}>
        <Route index element={<AdminDashboard />} />
      </Route>
      <Route path="/user-dashboard" element={<ProtectedRoute><DashboardLayout /></ProtectedRoute>}>
        <Route index element={<UserDashboard />} />
      </Route>
      <Route element={<ProtectedRoute requireAdmin={true}><DashboardLayout /></ProtectedRoute>}>
        <Route path="/admin/moods" element={<Moods />} />
        <Route path="/admin/books" element={<Books />} />
        <Route path="/admin/quizzes" element={<Quizzes />} />
        <Route path="/foods" element={<Foods />} />
        <Route path="/admin/menu-items" element={<Menu />} />
      </Route>
      <Route path="*" element={<Navigate to="/login" />} />
    </Routes>
  )
}

export default App
