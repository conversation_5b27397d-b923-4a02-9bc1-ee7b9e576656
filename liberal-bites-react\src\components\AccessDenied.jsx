import React from 'react';
import { useNavigate } from 'react-router-dom';

const AccessDenied = () => {
  const navigate = useNavigate();

  const styles = {
    container: {
      minHeight: '100vh',
      background: '#F5E8C7',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Inter, sans-serif'
    },
    card: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '24px',
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      padding: '48px',
      width: '100%',
      maxWidth: '500px',
      margin: '32px 16px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      textAlign: 'center'
    },
    iconContainer: {
      width: '80px',
      height: '80px',
      borderRadius: '50%',
      backgroundColor: 'rgba(255, 111, 97, 0.1)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0 auto 24px',
      border: '2px solid rgba(255, 111, 97, 0.2)'
    },
    icon: {
      fontSize: '32px',
      color: '#FF6F61'
    },
    title: {
      fontSize: '28px',
      fontWeight: 'bold',
      color: '#006D77',
      marginBottom: '16px'
    },
    message: {
      fontSize: '16px',
      color: '#4b5563',
      marginBottom: '32px',
      lineHeight: '1.6'
    },
    buttonContainer: {
      display: 'flex',
      gap: '16px',
      justifyContent: 'center',
      flexWrap: 'wrap'
    },
    button: {
      padding: '12px 24px',
      borderRadius: '12px',
      border: 'none',
      cursor: 'pointer',
      fontWeight: '600',
      fontSize: '14px',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    },
    primaryButton: {
      backgroundColor: '#FF6F61',
      color: 'white',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      color: '#006D77',
      border: '2px solid #006D77'
    }
  };

  const handleGoHome = () => {
    navigate('/main-menu');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.iconContainer}>
          <i className="fas fa-shield-alt" style={styles.icon}></i>
        </div>
        
        <h1 style={styles.title}>Access Denied</h1>
        
        <p style={styles.message}>
          You don't have permission to access this page. This area is restricted to administrators only.
          <br /><br />
          If you believe this is an error, please contact your system administrator.
        </p>
        
        <div style={styles.buttonContainer}>
          <button
            onClick={handleGoHome}
            style={{
              ...styles.button,
              ...styles.primaryButton
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)';
              e.target.style.transform = 'scale(1.02)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#FF6F61';
              e.target.style.transform = 'scale(1)';
            }}
          >
            <i className="fas fa-home"></i>
            Go to Main Menu
          </button>
          
          <button
            onClick={handleGoBack}
            style={{
              ...styles.button,
              ...styles.secondaryButton
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.1)';
              e.target.style.transform = 'scale(1.02)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.transform = 'scale(1)';
            }}
          >
            <i className="fas fa-arrow-left"></i>
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
