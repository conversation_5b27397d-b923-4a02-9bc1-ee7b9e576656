<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'}; </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none;}
        body { font-family: 'Inter', sans-serif; }
    </style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "peach": "#FFEAA7",
        "orange": "#E17055",
        "red-accent": "#D63031"
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap">
<style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
</style>
</head>
<body class="bg-gradient-to-br from-peach via-yellow-100 to-orange-100 min-h-screen">
    
    <header id="header" class="w-full px-8 py-6 flex justify-between items-center">
        <div id="logo-section" class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-orange rounded-full flex items-center justify-center">
                <i class="fa-solid fa-utensils text-white text-lg"></i>
            </div>
            <span class="text-2xl font-bold text-gray-800">Liberal Bites</span>
        </div>
        
        <div id="user-section" class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-5.jpg" alt="User Avatar" class="w-10 h-10 rounded-full border-2 border-orange">
                <span class="text-gray-700 font-medium">Welcome back!</span>
            </div>
            <button class="text-gray-600 hover:text-red-accent transition-colors">
                <i class="fa-solid fa-cog text-lg"></i>
            </button>
        </div>
    </header>

    <main id="main-dashboard" class="container mx-auto px-8 py-12">
        
        <section id="welcome-section" class="text-center mb-16">
            <h1 class="text-5xl font-bold text-gray-800 mb-4">Welcome to Liberal Bites</h1>
            <p class="text-xl text-gray-600 font-medium">Find your perfect match today!</p>
            <div class="w-24 h-1 bg-gradient-to-r from-orange to-red-accent mx-auto mt-6 rounded-full"></div>
        </section>

        <section id="action-buttons-section" class="max-w-6xl mx-auto">
            <div class="grid md:grid-cols-2 gap-12">
                
                <div id="quiz-card" class="bg-white rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="text-center mb-8">
                        <div class="relative mx-auto mb-6 w-48 h-48">
                            <img class="w-full h-full object-cover rounded-2xl shadow-lg" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/cb8fc24017-3c15478ab15eee4f078c.png" alt="open book with clouds and dreamy atmosphere, soft lighting, pastel colors">
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Discover Your Taste</h3>
                        <p class="text-gray-600 mb-6">Take our personalized quiz to find foods that match your mood and preferences perfectly.</p>
                        <button class="bg-gradient-to-r from-orange to-red-accent text-white px-8 py-4 rounded-full font-semibold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 w-full">
                            <i class="fa-solid fa-clipboard-question mr-2"></i>
                            Start Quiz
                        </button>
                    </div>
                </div>

                <div id="explore-card" class="bg-white rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="text-center mb-8">
                        <div class="relative mx-auto mb-6 w-48 h-48">
                            <img class="w-full h-full object-cover rounded-2xl shadow-lg" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/e3e31fe33c-6e2517f97ca24b3a3a6c.png" alt="delicious pasta dish with fresh ingredients, colorful vegetables, restaurant quality plating">
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Explore Delicious Food</h3>
                        <p class="text-gray-600 mb-6">Browse through our curated collection of amazing dishes and discover new flavors to try.</p>
                        <button class="bg-gradient-to-r from-orange to-red-accent text-white px-8 py-4 rounded-full font-semibold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 w-full">
                            <i class="fa-solid fa-compass mr-2"></i>
                            Explore Food
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <section id="stats-section" class="mt-20">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                
                <div id="stat-recipes" class="text-center bg-white/50 backdrop-blur-sm rounded-2xl p-6">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange to-red-accent rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-bowl-food text-white text-2xl"></i>
                    </div>
                    <h4 class="text-3xl font-bold text-gray-800 mb-2">1,000+</h4>
                    <p class="text-gray-600">Recipes Available</p>
                </div>

                <div id="stat-users" class="text-center bg-white/50 backdrop-blur-sm rounded-2xl p-6">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange to-red-accent rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-users text-white text-2xl"></i>
                    </div>
                    <h4 class="text-3xl font-bold text-gray-800 mb-2">50K+</h4>
                    <p class="text-gray-600">Happy Users</p>
                </div>

                <div id="stat-matches" class="text-center bg-white/50 backdrop-blur-sm rounded-2xl p-6">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange to-red-accent rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-heart text-white text-2xl"></i>
                    </div>
                    <h4 class="text-3xl font-bold text-gray-800 mb-2">98%</h4>
                    <p class="text-gray-600">Match Success</p>
                </div>
            </div>
        </section>
    </main>

    <footer id="footer" class="mt-20 text-center py-8">
        <p class="text-gray-600">© 2024 Liberal Bites. Made with <i class="fa-solid fa-heart text-red-accent"></i> for food lovers.</p>
    </footer>
</body>
</html>