<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
    <style>::-webkit-scrollbar { display: none;}</style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "warm-coral": "#FF6F61",
        "deep-teal": "#006D77",
        "warm-beige": "#F5E8C7",
        "soft-peach": "#FFDAB9",
        "dark-gray": "#333333"
      },
      "fontFamily": {
        "montserrat": [
          "Montserrat",
          "sans-serif"
        ],
        "sans": [
          "Inter",
          "sans-serif"
        ]
      },
      "animation": {
        "float": "float 8s ease-in-out infinite",
        "pulse-light": "pulse-light 6s ease-in-out infinite",
        "gradient-shift": "gradient-shift 10s ease infinite",
        "bokeh-fade": "bokeh-fade 8s ease-in-out infinite"
      },
      "keyframes": {
        "float": {
          "0%, 100%": {
            "transform": "translateY(0)"
          },
          "50%": {
            "transform": "translateY(-20px)"
          }
        },
        "pulse-light": {
          "0%, 100%": {
            "opacity": "0.3"
          },
          "50%": {
            "opacity": "0.7"
          }
        },
        "gradient-shift": {
          "0%, 100%": {
            "backgroundPosition": "0% 50%"
          },
          "50%": {
            "backgroundPosition": "100% 50%"
          }
        },
        "bokeh-fade": {
          "0%, 100%": {
            "opacity": "0.2"
          },
          "50%": {
            "opacity": "0.6"
          }
        }
      }
    }
  }
};</script>
    <style>
        .light-particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            box-shadow: 0 0 10px 2px rgba(255, 111, 97, 0.2);
            pointer-events: none;
        }
        
        .bokeh {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 111, 97, 0.3) 0%, rgba(255, 111, 97, 0) 70%);
            opacity: 0.3;
            filter: blur(3px);
            pointer-events: none;
        }
        
        .light-ray {
            position: absolute;
            background: linear-gradient(45deg, rgba(255, 111, 97, 0.05) 0%, rgba(255, 218, 185, 0.1) 100%);
            transform: rotate(45deg);
            transform-origin: center;
            pointer-events: none;
        }
        
        .gradient-bg {
            background: linear-gradient(-45deg, #F5E8C7, #FFDAB9, #F5E8C7, #FFE2C7);
            background-size: 400% 400%;
            animation: gradient-shift 10s ease infinite;
        }
        
        @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style>
</head>
<body class="font-montserrat bg-warm-beige overflow-hidden">
    <div id="login-container" class="relative w-full h-screen flex items-center justify-center gradient-bg">
        <!-- Animated background elements -->
        <div id="animated-bg" class="absolute inset-0 overflow-hidden">
            <!-- Light particles will be added via JS -->
            
            <!-- Bokeh effects -->
            <div class="bokeh w-32 h-32 top-10 left-20 animate-bokeh-fade"></div>
            <div class="bokeh w-24 h-24 top-1/4 right-1/3 animate-bokeh-fade" style="animation-delay: 2s;"></div>
            <div class="bokeh w-40 h-40 bottom-1/4 left-1/3 animate-bokeh-fade" style="animation-delay: 4s;"></div>
            <div class="bokeh w-20 h-20 bottom-10 right-20 animate-bokeh-fade" style="animation-delay: 3s;"></div>
            
            <!-- Light rays -->
            <div class="light-ray w-[800px] h-32 left-[-200px] top-1/3 animate-pulse-light"></div>
            <div class="light-ray w-[600px] h-24 right-[-100px] top-2/3 animate-pulse-light" style="animation-delay: 3s;"></div>
        </div>
        
        <div id="login-content" class="relative z-10 flex flex-col md:flex-row max-w-5xl w-full mx-4 md:mx-auto bg-white rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.1)] overflow-hidden">
            <!-- Left side with hero image -->
            <div id="hero-section" class="relative w-full md:w-1/2 h-64 md:h-auto overflow-hidden">
                <div class="absolute inset-0 bg-warm-coral opacity-30 z-10"></div>
                <div class="absolute inset-0 bg-gradient-to-t from-deep-teal/20 to-transparent z-10"></div>
                
                <!-- Pulsing light effect behind image -->
                <div class="absolute inset-0 bg-warm-coral/20 rounded-full blur-3xl scale-90 animate-pulse-light z-0"></div>
                
                <img class="absolute inset-0 w-full h-full object-cover z-5" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/46df5f6708-9f16babf9a425852687d.png" alt="Flat lay composition with books, cup of tea, pastries, and fruits on warm beige background. Books about mindfulness and wellness. Soft natural lighting from above. Minimalist styling with negative space. No people or human figures.">
                
                <div class="absolute bottom-0 left-0 right-0 p-6 z-20">
                    <h2 class="text-white text-xl font-medium drop-shadow-md">Nourish Your Mind, Feed Your Soul</h2>
                </div>
            </div>
            
            <!-- Right side with login form -->
            <div id="form-section" class="w-full md:w-1/2 p-8 md:p-10">
                <div class="flex justify-center mb-6">
                    <div class="w-48">
                        <h1 class="text-2xl font-bold text-deep-teal text-center">LiberateBites</h1>
                    </div>
                </div>
                
                <h2 class="text-2xl font-semibold text-dark-gray text-center mb-8">Welcome Back</h2>
                
                <form>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-deep-teal mb-2" for="email">
                            Email Address
                        </label>
                        <div class="relative">
                            <input id="email" type="email" class="w-full px-4 py-3 border border-deep-teal/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-warm-coral/50 focus:border-warm-coral transition-all duration-200" placeholder="<EMAIL>">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fa-regular fa-envelope text-deep-teal/60"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-2">
                            <label class="block text-sm font-medium text-deep-teal" for="password">
                                Password
                            </label>
                            <span class="text-xs text-warm-coral hover:text-warm-coral/80 transition-colors duration-200 cursor-pointer">
                                Forgot Password?
                            </span>
                        </div>
                        <div class="relative">
                            <input id="password" type="password" class="w-full px-4 py-3 border border-deep-teal/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-warm-coral/50 focus:border-warm-coral transition-all duration-200" placeholder="••••••••">
                            <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <i class="fa-regular fa-eye text-deep-teal/60"></i>
                            </button>
                        </div>
                    </div>
                    
                    <button id="login-button" type="submit" class="w-full bg-warm-coral hover:bg-warm-coral/90 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center group">
                        <span>Log In</span>
                        <i class="fa-solid fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-200"></i>
                    </button>
                </form>
                
                <div class="mt-6 text-center">
                    <p class="text-sm text-deep-teal/80">
                        Don't have an account? 
                        <span class="text-warm-coral font-medium hover:underline cursor-pointer">
                            Sign Up
                        </span>
                    </p>
                </div>
                
                <div class="mt-8 pt-6 border-t border-deep-teal/10">
                    <div class="flex justify-center space-x-4">
                        <button class="p-2 rounded-full border border-deep-teal/20 hover:bg-soft-peach/30 transition-colors duration-200">
                            <i class="fa-brands fa-google text-deep-teal"></i>
                        </button>
                        <button class="p-2 rounded-full border border-deep-teal/20 hover:bg-soft-peach/30 transition-colors duration-200">
                            <i class="fa-brands fa-apple text-deep-teal"></i>
                        </button>
                        <button class="p-2 rounded-full border border-deep-teal/20 hover:bg-soft-peach/30 transition-colors duration-200">
                            <i class="fa-brands fa-facebook-f text-deep-teal"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Create floating light particles
        function createLightParticles() {
            const container = document.getElementById('animated-bg');
            const particleCount = 30;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('light-particle');
                
                // Random size between 4px and 12px
                const size = Math.random() * 8 + 4;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                
                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                
                // Random opacity
                particle.style.opacity = (Math.random() * 0.5 + 0.2).toString();
                
                // Animation
                const duration = Math.random() * 20 + 10; // 10-30s
                const delay = Math.random() * 10;
                
                particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;
                
                container.appendChild(particle);
            }
        }
        
        // Initialize animations
        document.addEventListener('DOMContentLoaded', () => {
            createLightParticles();
            
            // Add hover effect to login button
            const loginButton = document.getElementById('login-button');
            loginButton.addEventListener('mouseenter', () => {
                loginButton.classList.add('shadow-lg');
            });
            loginButton.addEventListener('mouseleave', () => {
                loginButton.classList.remove('shadow-lg');
            });
            
            // Add focus effects to input fields
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    input.parentElement.classList.add('relative');
                    const glow = document.createElement('div');
                    glow.classList.add('absolute', 'inset-0', 'rounded-lg', 'pointer-events-none');
                    glow.style.boxShadow = '0 0 0 3px rgba(255, 111, 97, 0.1)';
                    glow.style.zIndex = '-1';
                    input.parentElement.appendChild(glow);
                });
                
                input.addEventListener('blur', () => {
                    const glow = input.parentElement.querySelector('div:last-child');
                    if (glow && glow.style.boxShadow) {
                        input.parentElement.removeChild(glow);
                    }
                });
            });
        });
    </script>
</body>
</html>