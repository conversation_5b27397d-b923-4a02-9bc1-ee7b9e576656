<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Mood;
use App\Models\SubMood;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MoodDataUpdateTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that mood data update creates correct number of records
     */
    public function test_mood_data_update_creates_correct_counts(): void
    {
        // Initially should be empty
        $this->assertEquals(0, Mood::count());
        $this->assertEquals(0, SubMood::count());

        // Trigger the update via the web route
        $response = $this->get('/update-mood-data');
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Check counts
        $this->assertEquals(20, Mood::count());
        $this->assertEquals(100, SubMood::count());
    }

    /**
     * Test that specific mood categories exist
     */
    public function test_specific_mood_categories_exist(): void
    {
        // Trigger update
        $this->get('/update-mood-data');

        // Check for specific mood categories
        $expectedMoods = [
            'Overwhelmed Mind',
            'Low Self-Worth',
            'Emotional Burnout',
            'Fear & Anxiety',
            'Seeker\'s Soul'
        ];

        foreach ($expectedMoods as $moodName) {
            $this->assertDatabaseHas('moods', ['name' => $moodName]);
        }
    }

    /**
     * Test that sub-moods have correct structure
     */
    public function test_sub_moods_have_correct_structure(): void
    {
        // Trigger update
        $this->get('/update-mood-data');

        // Get first mood and check its sub-moods
        $overwhelmedMind = Mood::where('name', 'Overwhelmed Mind')->first();
        $this->assertNotNull($overwhelmedMind);

        $subMoods = $overwhelmedMind->subMoods;
        $this->assertEquals(5, $subMoods->count());

        // Check specific sub-mood
        $overthinking = $subMoods->where('name', 'Overthinking')->first();
        $this->assertNotNull($overthinking);
        $this->assertEquals('Constantly replaying thoughts and scenarios', $overthinking->meaning);
    }

    /**
     * Test mood data status endpoint
     */
    public function test_mood_data_status_endpoint(): void
    {
        // Trigger update first
        $this->get('/update-mood-data');

        // Check status endpoint
        $response = $this->get('/mood-data-status');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_moods',
            'total_sub_moods',
            'moods' => [
                '*' => [
                    'id',
                    'name',
                    'sub_moods_count',
                    'sub_moods' => [
                        '*' => [
                            'name',
                            'meaning'
                        ]
                    ]
                ]
            ]
        ]);

        $data = $response->json();
        $this->assertEquals(20, $data['total_moods']);
        $this->assertEquals(100, $data['total_sub_moods']);
    }

    /**
     * Test that update is idempotent (can run multiple times safely)
     */
    public function test_update_is_idempotent(): void
    {
        // Run update twice
        $this->get('/update-mood-data');
        $this->get('/update-mood-data');

        // Should still have correct counts
        $this->assertEquals(20, Mood::count());
        $this->assertEquals(100, SubMood::count());
    }

    /**
     * Test that all moods have exactly 5 sub-moods
     */
    public function test_all_moods_have_five_sub_moods(): void
    {
        // Trigger update
        $this->get('/update-mood-data');

        $moods = Mood::with('subMoods')->get();
        
        foreach ($moods as $mood) {
            $this->assertEquals(5, $mood->subMoods->count(), 
                "Mood '{$mood->name}' should have exactly 5 sub-moods");
        }
    }

    /**
     * Test that all sub-moods have meanings
     */
    public function test_all_sub_moods_have_meanings(): void
    {
        // Trigger update
        $this->get('/update-mood-data');

        $subMoods = SubMood::all();
        
        foreach ($subMoods as $subMood) {
            $this->assertNotEmpty($subMood->meaning, 
                "Sub-mood '{$subMood->name}' should have a meaning");
        }
    }
}
