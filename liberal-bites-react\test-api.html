<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <div id="results"></div>
    <button onclick="testGetBooks()">Test Get Books</button>
    <button onclick="testAddBook()">Test Add Book</button>

    <script>
        const API_BASE_URL = 'http://***************/cstm-api/api';
        
        const getAuthHeaders = () => {
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer test-token`
            };
        };

        async function testGetBooks() {
            try {
                const response = await fetch(`${API_BASE_URL}/books`, {
                    method: 'GET',
                    headers: getAuthHeaders(),
                });
                
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h3>Get Books Result:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testAddBook() {
            try {
                const bookData = {
                    title: "Test Book",
                    author: "Test Author",
                    description: "Test Description",
                    sub_mood_id: 1
                };

                const response = await fetch(`${API_BASE_URL}/books`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(bookData),
                });
                
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h3>Add Book Result:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
