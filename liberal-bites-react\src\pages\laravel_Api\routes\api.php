<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\MoodController;
use App\Http\Controllers\API\SubMoodController;
use App\Http\Controllers\API\BookController;
use App\Http\Controllers\API\FoodItemController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\McqController;

Route::post('/admin/login', [AuthController::class, 'login']);
Route::post('/admin/register', [AuthController::class, 'register']);

Route::middleware('auth:sanctum')->group(function () {
    Route::get('/admin/me', [AuthController::class, 'me']);
    Route::post('/admin/logout', [AuthController::class, 'logout']);
    Route::apiResource('food-items', FoodItemController::class);
    Route::apiResource('books', BookController::class);
    Route::apiResource('moods', MoodController::class);
    Route::apiResource('sub-moods', SubMoodController::class);
    Route::get('/mcqs', [McqController::class, 'index']);
    Route::get('/mcqs/{id}', [McqController::class, 'show']);
    Route::post('/mcqs', [McqController::class, 'store']);
    Route::put('/mcqs/{id}', [McqController::class, 'update']);
    Route::delete('/mcqs/{id}', [McqController::class, 'destroy']);
    Route::delete('/mcqs/options/{id}', [McqController::class, 'destroyOption']);

});