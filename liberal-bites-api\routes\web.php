<?php

use Illuminate\Support\Facades\Route;
use App\Models\Mood;
use App\Models\SubMood;
//require base_path('routes/api.php');

Route::get('/', function () {
    return view('welcome');
});

// Liberal Bites: Manual mood data update route (for testing)
Route::get('/update-mood-data', function () {
    try {
        // Clear existing data
        Mood::truncate();

        // Get the AppServiceProvider instance and call the update method
        $provider = new \App\Providers\AppServiceProvider(app());
        $reflection = new \ReflectionClass($provider);

        // Call private methods using reflection
        $updateMethod = $reflection->getMethod('updateMoodData');
        $updateMethod->setAccessible(true);
        $updateMethod->invoke($provider);

        return response()->json([
            'success' => true,
            'message' => 'Mood data updated successfully!',
            'mood_count' => Mood::count(),
            'sub_mood_count' => SubMood::count()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error updating mood data: ' . $e->getMessage()
        ], 500);
    }
});

// Liberal Bites: Check mood data status
Route::get('/mood-data-status', function () {
    $moods = Mood::with('subMoods')->get();

    return response()->json([
        'total_moods' => $moods->count(),
        'total_sub_moods' => SubMood::count(),
        'moods' => $moods->map(function ($mood) {
            return [
                'id' => $mood->id,
                'name' => $mood->name,
                'sub_moods_count' => $mood->subMoods->count(),
                'sub_moods' => $mood->subMoods->map(function ($subMood) {
                    return [
                        'name' => $subMood->name,
                        'meaning' => $subMood->meaning
                    ];
                })
            ];
        })
    ]);
});
