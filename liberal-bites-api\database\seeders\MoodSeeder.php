<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Mood;

class MoodSeeder extends Seeder
{
    public function run(): void
    {
        $moods = [
            'Happy', 'Sad', 'Angry', 'Relaxed', 'Romantic',
            'Anxious', 'Lonely', 'Energetic', 'Bored', 'Motivated',
            'Hopeful', 'Depressed', 'Inspired', 'Stressed', 'Excited',
            'Peaceful', 'Confused', 'Grateful', 'Tired', 'Curious'
        ];

        foreach ($moods as $mood) {
            Mood::create(['name' => $mood]);
        }
    }
}
