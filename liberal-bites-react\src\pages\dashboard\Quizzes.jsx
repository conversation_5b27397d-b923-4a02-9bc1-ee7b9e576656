import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { quizzesAPI } from '../../services/api';
import DashboardSidebar from '../../components/DashboardSidebar';

function Quizzes() {
  const navigate = useNavigate();
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedMood, setSelectedMood] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [userName, setUserName] = useState('Aisha');

  // CRUD form states
  const [showCrudForm, setShowCrudForm] = useState(false);
  const [formMode, setFormMode] = useState('add'); // 'add', 'edit'
  const [editingQuiz, setEditingQuiz] = useState(null);
  const [formData, setFormData] = useState({
    question: '',
    options: []
  });
  const [formLoading, setFormLoading] = useState(false);
  const [formMessage, setFormMessage] = useState('');

  // Option form states
  const [newOption, setNewOption] = useState({ option_text: '', points: 1 });
  const [editingOptionIndex, setEditingOptionIndex] = useState(-1);

  // Form field focus states
  const [questionFocused, setQuestionFocused] = useState(false);
  const [optionTextFocused, setOptionTextFocused] = useState(false);
  const [optionPointsFocused, setOptionPointsFocused] = useState(false);

  // Form field hover states
  const [questionHovered, setQuestionHovered] = useState(false);
  const [submitHovered, setSubmitHovered] = useState(false);
  const [cancelHovered, setCancelHovered] = useState(false);

  // Mood categories
  const moodCategories = [
    { id: 'all', name: 'All Quizzes', color: '#6b7280' },
    { id: 'stressed', name: 'Feeling Stressed', color: '#ef4444' },
    { id: 'energy', name: 'Need Energy', color: '#eab308' },
    { id: 'comfort', name: 'Seeking Comfort', color: '#a855f7' },
    { id: 'focus', name: 'Need Focus', color: '#3b82f6' },
    { id: 'calm', name: 'Want Calm', color: '#10b981' }
  ];

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
    
    fetchQuizzes();
  }, []);

  const fetchQuizzes = async () => {
    try {
      setLoading(true);
      const data = await quizzesAPI.getAll();
      console.log('Quiz data received:', data);
      setQuizzes(data);
      setError('');
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      setError(error.message || 'Error loading quizzes');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    setFormMessage('');

    try {
      if (formMode === 'add') {
        await quizzesAPI.create(formData);
        setFormMessage('Quiz added successfully!');
      } else {
        await quizzesAPI.update(editingQuiz.id, formData);
        setFormMessage('Quiz updated successfully!');
      }

      // Refresh quizzes list
      await fetchQuizzes();

      // Reset form after short delay
      setTimeout(() => {
        resetForm();
        setShowCrudForm(false);
      }, 1500);
    } catch (error) {
      console.error('Error saving quiz:', error);
      setFormMessage(error.message || 'Error saving quiz');
    } finally {
      setFormLoading(false);
    }
  };



  const handleDeleteQuiz = async (quizId) => {
    if (window.confirm('Are you sure you want to delete this quiz?')) {
      try {
        await quizzesAPI.delete(quizId);
        await fetchQuizzes(); // Refresh the list
      } catch (error) {
        console.error('Error deleting quiz:', error);
        alert(error.message || 'Error deleting quiz');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      question: '',
      options: []
    });
    setEditingQuiz(null);
    setFormMode('add');
    setFormMessage('');
    setNewOption({ option_text: '', points: 1 });
    setEditingOptionIndex(-1);
  };

  // Filter quizzes based on search term and selected mood
  const filteredQuizzes = quizzes.filter(quiz => {
    const matchesSearch = !searchTerm || 
                         (quiz.question || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (quiz.options || []).some(option => 
                           (option.option_text || '').toLowerCase().includes(searchTerm.toLowerCase())
                         );

    if (selectedMood === 'all') return matchesSearch;

    const matchesMood = quiz.options && quiz.options.some(option =>
      (option.option_text || '').toLowerCase().includes(selectedMood) ||
      (quiz.question || '').toLowerCase().includes(selectedMood)
    );

    return matchesSearch && matchesMood;
  });

  const getMoodColor = (questionText) => {
    if (!questionText || typeof questionText !== 'string') {
      return '#6b7280';
    }
    const mood = moodCategories.find(m =>
      questionText.toLowerCase().includes(m.id) ||
      m.name.toLowerCase().includes(questionText.toLowerCase())
    );
    return mood ? mood.color : '#6b7280';
  };

  // Option management functions
  const addOption = () => {
    if (newOption.option_text.trim()) {
      const updatedOptions = [...formData.options, {
        id: Date.now(), // Temporary ID for new options
        option_text: newOption.option_text.trim(),
        points: parseInt(newOption.points) || 1
      }];
      setFormData({...formData, options: updatedOptions});
      setNewOption({ option_text: '', points: 1 });
    }
  };

  const editOption = (index) => {
    const option = formData.options[index];
    setNewOption({ option_text: option.option_text, points: option.points });
    setEditingOptionIndex(index);
  };

  const updateOption = () => {
    if (newOption.option_text.trim() && editingOptionIndex >= 0) {
      const updatedOptions = [...formData.options];
      updatedOptions[editingOptionIndex] = {
        ...updatedOptions[editingOptionIndex],
        option_text: newOption.option_text.trim(),
        points: parseInt(newOption.points) || 1
      };
      setFormData({...formData, options: updatedOptions});
      setNewOption({ option_text: '', points: 1 });
      setEditingOptionIndex(-1);
    }
  };

  const deleteOption = (index) => {
    const updatedOptions = formData.options.filter((_, i) => i !== index);
    setFormData({...formData, options: updatedOptions});
  };

  const cancelOptionEdit = () => {
    setNewOption({ option_text: '', points: 1 });
    setEditingOptionIndex(-1);
  };

  // CRUD Functions
  const handleAdd = () => {
    setFormMode('add');
    setEditingQuiz(null);
    setFormData({
      question: '',
      options: []
    });
    setNewOption({ option_text: '', points: 1 });
    setEditingOptionIndex(-1);
    setFormMessage('');
    setShowCrudForm(true);
  };

  const handleEditQuiz = (quiz) => {
    setFormMode('edit');
    setEditingQuiz(quiz);
    setFormData({
      question: quiz.question || '',
      options: quiz.options || []
    });
    setNewOption({ option_text: '', points: 1 });
    setEditingOptionIndex(-1);
    setFormMessage('');
    setShowCrudForm(true);
  };





  const handleFormCancel = () => {
    setShowCrudForm(false);
    setFormData({
      question: '',
      options: []
    });
    setNewOption({ option_text: '', points: 1 });
    setEditingOptionIndex(-1);
    setFormMessage('');
  };

  const openAddForm = () => handleAdd();
  const openEditForm = (quiz) => handleEditQuiz(quiz);
  const closeCrudForm = () => handleFormCancel();

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      {/* Dashboard Sidebar */}
      <DashboardSidebar />

      {/* Main Content */}
      <div style={{ marginLeft: '256px', minHeight: '100vh' }}>
        {/* Header */}


        {/* Content Area */}
        <div style={{ padding: '32px' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 300px', gap: '32px', marginBottom: '32px' }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h1 style={{
                  fontSize: '28px',
                  fontWeight: '700',
                  color: '#1f2937',
                  marginBottom: '8px'
                }}>Quiz Questions for {userName}!</h1>
                <p style={{ color: '#4b5563', marginTop: '4px' }}>Manage quiz questions and their answer options</p>

                {/* Search Bar */}
                <div style={{ position: 'relative', marginTop: '16px', maxWidth: '100%' }}>
                  <input
                    type="text"
                    placeholder="Search quiz questions by text or options..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      width: '100%',
                      maxWidth: '100%',
                      padding: '12px 40px 12px 16px',
                      border: '1px solid rgba(0, 109, 119, 0.3)',
                      borderRadius: '8px',
                      fontSize: '14px',
                      outline: 'none',
                      backgroundColor: '#f9fafb',
                      boxSizing: 'border-box'
                    }}
                  />
                  <i className="fa-solid fa-search" style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'rgba(0, 109, 119, 0.6)'
                  }}></i>
                </div>

                {/* Mood Filter Tabs */}
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px',
                  marginTop: '16px'
                }}>
                  {moodCategories.map(mood => (
                    <button
                      key={mood.id}
                      onClick={() => setSelectedMood(mood.id)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '9999px',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: '500',
                        backgroundColor: selectedMood === mood.id ? mood.color : `${mood.color}20`,
                        color: selectedMood === mood.id ? 'white' : mood.color
                      }}
                    >
                      {mood.name}
                    </button>
                  ))}
                </div>

                {/* Add New Quiz Button */}
                <div style={{ marginTop: '16px' }}>
                  <button
                    onClick={() => handleAdd()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: '#FF6F61',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                  >
                    <i className="fa-solid fa-plus"></i>
                    Add New Quiz Question
                  </button>
                </div>
              </div>
            </div>

            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>Quiz Stats</h2>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: 'rgba(0, 109, 119, 0.1)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 8px'
                    }}>
                      <i className="fa-solid fa-question-circle" style={{ color: '#006D77', fontSize: '20px' }}></i>
                    </div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>{quizzes.length}</div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>Total Questions</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quiz Questions Display Section */}
          <div style={{ marginBottom: '32px' }}>
            {loading ? (
              <div style={{ textAlign: 'center', padding: '64px 0' }}>
                <div style={{
                  display: 'inline-block',
                  width: '32px',
                  height: '32px',
                  border: '3px solid #f3f4f6',
                  borderTop: '3px solid #006D77',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <p style={{ marginTop: '16px', color: '#6b7280' }}>Loading quiz questions...</p>
              </div>
            ) : error ? (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-exclamation-triangle" style={{ fontSize: '32px', color: '#ef4444', marginBottom: '16px' }}></i>
                <p style={{ color: '#ef4444', fontSize: '16px', marginBottom: '16px' }}>{error}</p>
                <button
                  onClick={fetchQuizzes}
                  style={{
                    backgroundColor: '#FF6F61',
                    color: 'white',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Try Again
                </button>
              </div>
            ) : filteredQuizzes.length === 0 ? (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-question-circle" style={{ fontSize: '32px', color: '#6b7280', marginBottom: '16px' }}></i>
                <p style={{ color: '#6b7280', fontSize: '16px', marginBottom: '16px' }}>
                  {searchTerm || selectedMood !== 'all' ? 'No quiz questions match your search criteria.' : 'No quiz questions available.'}
                </p>
                {(searchTerm || selectedMood !== 'all') && (
                  <div style={{ display: 'flex', justifyContent: 'center', gap: '12px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <button
                        onClick={openAddForm}
                        style={{
                          padding: '8px 16px',
                          backgroundColor: '#FF6F61',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          cursor: 'pointer',
                          fontSize: '14px',
                          fontWeight: '500',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          transition: 'background-color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                        onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                      >
                        <i className="fa-solid fa-plus"></i>
                        Add Quiz Question
                      </button>
                      <div style={{
                        width: '1px',
                        height: '24px',
                        backgroundColor: '#e5e7eb'
                      }}></div>
                      <button
                        onClick={() => {
                          setSearchTerm('');
                          setSelectedMood('all');
                        }}
                        style={{
                          backgroundColor: '#FF6F61',
                          color: 'white',
                          padding: '12px 24px',
                          borderRadius: '8px',
                          border: 'none',
                          cursor: 'pointer',
                          fontSize: '14px',
                          fontWeight: '500'
                        }}
                      >
                        Clear Filters
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 1fr)',
                gap: '24px'
              }}>
                {filteredQuizzes.map((quiz) => (
                  <div
                    key={quiz.id}
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      border: '1px solid #e5e7eb',
                      overflow: 'hidden',
                      transition: 'all 0.2s ease',
                      cursor: 'pointer',
                      height: 'fit-content'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                    }}
                  >
                    <div style={{
                      height: '120px',
                      background: 'linear-gradient(135deg, #006D77 0%, #83C5BE 100%)',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-question-circle" style={{ fontSize: '36px', color: 'white', opacity: 0.8 }}></i>
                    </div>

                    <div style={{ padding: '20px', flex: 1, display: 'flex', flexDirection: 'column' }}>
                      <h3 style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#1f2937',
                        marginBottom: '12px',
                        lineHeight: '1.4'
                      }}>
                        {quiz.question || 'Untitled Question'}
                      </h3>

                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        marginBottom: '16px'
                      }}>
                        {quiz.options ? quiz.options.length : 0} answer options
                      </div>

                      {quiz.options && quiz.options.length > 0 && (
                        <div style={{ marginBottom: '16px' }}>
                          <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '6px'
                          }}>
                            {quiz.options.slice(0, 3).map((option, index) => (
                              <div
                                key={option.id || index}
                                style={{
                                  padding: '6px 10px',
                                  backgroundColor: '#f3f4f6',
                                  borderRadius: '6px',
                                  fontSize: '12px',
                                  color: '#374151',
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  alignItems: 'center'
                                }}
                              >
                                <span>{option.option_text || 'Unknown Option'}</span>
                                <span style={{
                                  backgroundColor: getMoodColor(option.option_text || ''),
                                  color: 'white',
                                  padding: '2px 6px',
                                  borderRadius: '10px',
                                  fontSize: '10px',
                                  fontWeight: '500'
                                }}>
                                  {option.points || 0} pts
                                </span>
                              </div>
                            ))}
                            {quiz.options.length > 3 && (
                              <div style={{
                                padding: '6px 10px',
                                backgroundColor: '#f3f4f6',
                                color: '#6b7280',
                                borderRadius: '6px',
                                fontSize: '12px',
                                textAlign: 'center',
                                fontStyle: 'italic'
                              }}>
                                +{quiz.options.length - 3} more options
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div style={{ display: 'flex', gap: '8px', marginTop: 'auto' }}>
                        <button
                          onClick={() => openEditForm(quiz)}
                          style={{
                            flex: 1,
                            backgroundColor: '#006D77',
                            color: 'white',
                            padding: '10px 16px',
                            borderRadius: '8px',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'background-color 0.2s ease'
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.9)'}
                          onMouseLeave={(e) => e.target.style.backgroundColor = '#006D77'}
                        >
                          <i className="fa-solid fa-edit" style={{ marginRight: '6px' }}></i>
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteQuiz(quiz.id)}
                          style={{
                            flex: 1,
                            backgroundColor: '#dc2626',
                            color: 'white',
                            padding: '10px 16px',
                            borderRadius: '8px',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'background-color 0.2s ease'
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(220, 38, 38, 0.9)'}
                          onMouseLeave={(e) => e.target.style.backgroundColor = '#dc2626'}
                        >
                          <i className="fa-solid fa-trash" style={{ marginRight: '6px' }}></i>
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CRUD Form Modal */}
      {showCrudForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            width: '100%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflowY: 'auto',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            position: 'relative'
          }}>
            <h2 style={{
              fontSize: '24px',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '24px',
              textAlign: 'center'
            }}>
              {formMode === 'add' ? 'Add New Quiz Question' : 'Edit Quiz Question'}
            </h2>

            <form onSubmit={handleSubmit}>
              {/* Question Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Question Text *
                </label>
                <textarea
                  value={formData.question}
                  onChange={(e) => setFormData({...formData, question: e.target.value})}
                  onFocus={() => setQuestionFocused(true)}
                  onBlur={() => setQuestionFocused(false)}
                  onMouseEnter={() => setQuestionHovered(true)}
                  onMouseLeave={() => setQuestionHovered(false)}
                  required
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `2px solid ${questionFocused ? '#006D77' : (questionHovered ? 'rgba(0, 109, 119, 0.3)' : '#e5e7eb')}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: questionFocused ? '#f0fdfa' : 'white',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                  placeholder="Enter your quiz question (e.g., What do you feel about topic 1?)"
                />
              </div>

              {/* Answer Options Section */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '12px'
                }}>
                  Answer Options ({formData.options.length})
                </label>

                {/* Add/Edit Option Form */}
                <div style={{
                  backgroundColor: '#f9fafb',
                  padding: '16px',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb',
                  marginBottom: '16px'
                }}>
                  <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr auto', gap: '12px', alignItems: 'end' }}>
                    <div>
                      <label style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', display: 'block' }}>
                        Option Text
                      </label>
                      <input
                        type="text"
                        value={newOption.option_text}
                        onChange={(e) => setNewOption({...newOption, option_text: e.target.value})}
                        onFocus={() => setOptionTextFocused(true)}
                        onBlur={() => setOptionTextFocused(false)}
                        style={{
                          width: '100%',
                          padding: '8px 12px',
                          border: `1px solid ${optionTextFocused ? '#006D77' : '#d1d5db'}`,
                          borderRadius: '6px',
                          fontSize: '14px',
                          outline: 'none',
                          transition: 'all 0.2s ease',
                          boxSizing: 'border-box'
                        }}
                        placeholder="e.g., Strongly Agree"
                      />
                    </div>
                    <div>
                      <label style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', display: 'block' }}>
                        Points
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={newOption.points}
                        onChange={(e) => setNewOption({...newOption, points: e.target.value})}
                        onFocus={() => setOptionPointsFocused(true)}
                        onBlur={() => setOptionPointsFocused(false)}
                        style={{
                          width: '100%',
                          padding: '8px 12px',
                          border: `1px solid ${optionPointsFocused ? '#006D77' : '#d1d5db'}`,
                          borderRadius: '6px',
                          fontSize: '14px',
                          outline: 'none',
                          transition: 'all 0.2s ease',
                          boxSizing: 'border-box'
                        }}
                        placeholder="1-10"
                      />
                    </div>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      {editingOptionIndex >= 0 ? (
                        <>
                          <button
                            type="button"
                            onClick={updateOption}
                            style={{
                              padding: '8px 12px',
                              backgroundColor: '#006D77',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '12px',
                              cursor: 'pointer',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            Update
                          </button>
                          <button
                            type="button"
                            onClick={cancelOptionEdit}
                            style={{
                              padding: '8px 12px',
                              backgroundColor: '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '12px',
                              cursor: 'pointer',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            Cancel
                          </button>
                        </>
                      ) : (
                        <button
                          type="button"
                          onClick={addOption}
                          style={{
                            padding: '8px 12px',
                            backgroundColor: '#FF6F61',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            whiteSpace: 'nowrap',
                            transition: 'background-color 0.2s ease'
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                          onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                        >
                          Add
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Options List */}
                {formData.options.length > 0 && (
                  <div style={{
                    maxHeight: '200px',
                    overflowY: 'auto',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px'
                  }}>
                    {formData.options.map((option, index) => (
                      <div key={index} style={{
                        padding: '12px 16px',
                        borderBottom: index < formData.options.length - 1 ? '1px solid #f3f4f6' : 'none',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        backgroundColor: editingOptionIndex === index ? '#f0fdfa' : 'white'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{ fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                            {option.option_text}
                          </div>
                          <div style={{ fontSize: '12px', color: '#6b7280' }}>
                            {option.points} points
                          </div>
                        </div>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <button
                            type="button"
                            onClick={() => editOption(index)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#006D77',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              fontSize: '12px',
                              cursor: 'pointer'
                            }}
                          >
                            Edit
                          </button>
                          <button
                            type="button"
                            onClick={() => deleteOption(index)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#dc2626',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              fontSize: '12px',
                              cursor: 'pointer'
                            }}
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Form Message */}
              {formMessage && (
                <div style={{
                  padding: '12px 16px',
                  backgroundColor: formMessage.includes('success') ? '#d1fae5' : '#fee2e2',
                  color: formMessage.includes('success') ? '#065f46' : '#991b1b',
                  borderRadius: '8px',
                  marginBottom: '20px',
                  fontSize: '14px',
                  textAlign: 'center'
                }}>
                  {formMessage}
                </div>
              )}

              {/* Form Buttons */}
              <div style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'flex-end'
              }}>
                <button
                  type="button"
                  onClick={handleFormCancel}
                  onMouseEnter={() => setCancelHovered(true)}
                  onMouseLeave={() => setCancelHovered(false)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: cancelHovered ? '#f3f4f6' : 'white',
                    color: '#374151',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  onMouseEnter={() => setSubmitHovered(true)}
                  onMouseLeave={() => setSubmitHovered(false)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: formLoading ? '#9ca3af' : (submitHovered ? 'rgba(255, 111, 97, 0.9)' : '#FF6F61'),
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: formLoading ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  {formLoading && (
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid transparent',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                  )}
                  {formLoading ? 'Processing...' : (formMode === 'add' ? 'Add Quiz Question' : 'Update Quiz Question')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default Quizzes;
