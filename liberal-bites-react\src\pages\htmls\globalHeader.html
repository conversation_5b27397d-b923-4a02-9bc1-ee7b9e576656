<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    window.FontAwesomeConfig = { autoReplaceSvg: 'nest' };
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <style>
    ::-webkit-scrollbar {
      display: none;
    }
  </style>
  <script>
    tailwind.config = {
      "theme": {
        "extend": {
          "colors": {
            "coral": "#FF6F61",
            "teal": "#006D77",
            "cream": "#F5E8C7",
            "creamDark": "#EAD9B7"
          },
          "fontFamily": {
            "inter": ["Inter", "sans-serif"],
            "sans": ["Inter", "sans-serif"]
          }
        }
      }
    };
  </script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
    }

    .nav-link {
      position: relative;
      transition: all 0.3s ease;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 0;
      height: 2px;
      background-color: #FF6F61;
      transition: width 0.3s ease;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    .header-gradient {
      background: linear-gradient(to right, #F5E8C7, #F7EFD6, #F5E8C7);
    }
  </style>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap">
  <style>
    .highlighted-section {
      outline: 2px solid #3F20FB;
      background-color: rgba(63, 32, 251, 0.1);
    }

    .edit-button {
      position: absolute;
      z-index: 1000;
    }

    ::-webkit-scrollbar {
      display: none;
    }

    html, body {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  </style>
</head>
<body class="bg-white">
  <header id="header" class="header-gradient font-inter h-[70px] md:h-[70px] fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out shadow-sm">
    <div class="container mx-auto px-4 h-full">
      <div class="flex items-center justify-between h-full">
        <!-- Logo Section -->
        <div id="logo-section" class="flex items-center">
          <div class="flex items-center">
            <div class="bg-coral w-10 h-10 rounded-full flex items-center justify-center shadow-sm">
              <span class="text-white font-bold text-lg">LB</span>
            </div>
            <span class="ml-3 text-teal font-semibold text-xl hidden sm:block">LiberateBites</span>
          </div>
        </div>

        <!-- Desktop Navigation -->
        <nav id="desktop-nav" class="hidden md:flex items-center space-x-8">
          <span class="nav-link text-teal font-medium hover:text-coral transition-colors duration-300 cursor-pointer">Home</span>
          <span class="nav-link text-teal font-medium hover:text-coral transition-colors duration-300 cursor-pointer">About</span>
          <span class="nav-link text-teal font-medium hover:text-coral transition-colors duration-300 cursor-pointer">Menu</span>
          <span class="nav-link text-teal font-medium hover:text-coral transition-colors duration-300 cursor-pointer">Contact</span>
        </nav>

        <!-- User Profile & Settings -->
        <div id="user-section" class="flex items-center space-x-4">
          <div class="hidden md:flex items-center">
            <button id="settings-btn" class="text-teal p-2 rounded-full hover:bg-creamDark transition-colors duration-300">
              <i class="fa-solid fa-sliders text-lg"></i>
            </button>
            <div class="ml-4 flex items-center">
              <div class="mr-3 text-right hidden sm:block">
                <p class="text-teal text-sm font-medium">Welcome back</p>
                <p class="text-coral text-xs">Alex Morgan</p>
              </div>
              <div class="w-10 h-10 rounded-full overflow-hidden border-2 border-coral">
                <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg" alt="User Avatar" class="w-full h-full object-cover">
              </div>
            </div>
          </div>

          <!-- Mobile Menu Button -->
          <button id="mobile-menu-btn" class="md:hidden text-teal p-2 rounded-full hover:bg-creamDark transition-colors duration-300">
            <i class="fa-solid fa-bars text-xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation Dropdown -->
    <div id="mobile-nav" class="md:hidden bg-cream absolute top-[60px] left-0 right-0 shadow-md hidden">
      <div class="container mx-auto px-4 py-4">
        <nav class="flex flex-col space-y-4">
          <span class="text-teal font-medium py-2 px-4 rounded hover:bg-creamDark transition-colors duration-300 cursor-pointer">Home</span>
          <span class="text-teal font-medium py-2 px-4 rounded hover:bg-creamDark transition-colors duration-300 cursor-pointer">About</span>
          <span class="text-teal font-medium py-2 px-4 rounded hover:bg-creamDark transition-colors duration-300 cursor-pointer">Menu</span>
          <span class="text-teal font-medium py-2 px-4 rounded hover:bg-creamDark transition-colors duration-300 cursor-pointer">Contact</span>
          <hr class="border-creamDark">
          <div class="flex items-center py-2 px-4">
            <div class="w-10 h-10 rounded-full overflow-hidden border-2 border-coral">
              <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg" alt="User Avatar" class="w-full h-full object-cover">
            </div>
            <div class="ml-3">
              <p class="text-teal text-sm font-medium">Alex Morgan</p>
              <p class="text-coral text-xs">Premium Member</p>
            </div>
          </div>
          <span class="flex items-center text-teal font-medium py-2 px-4 rounded hover:bg-creamDark transition-colors duration-300 cursor-pointer">
            <i class="fa-solid fa-sliders mr-2"></i> Settings
          </span>
        </nav>
      </div>
    </div>
  </header>

  <!-- Page content placeholder -->
  <div class="pt-[90px] h-[1000px] bg-white">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-4xl font-bold text-teal mb-4">Welcome to LiberateBites</h1>
      <p class="text-lg text-gray-700">Find dining options based on your mood.</p>
    </div>
  </div>

  <script>
    // Toggle mobile menu
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileNav = document.getElementById('mobile-nav');

    mobileMenuBtn.addEventListener('click', () => {
      if (mobileNav.classList.contains('hidden')) {
        mobileNav.classList.remove('hidden');
        mobileMenuBtn.innerHTML = '<i class="fa-solid fa-xmark text-xl"></i>';
      } else {
        mobileNav.classList.add('hidden');
        mobileMenuBtn.innerHTML = '<i class="fa-solid fa-bars text-xl"></i>';
      }
    });

    // Add shadow on scroll
    window.addEventListener('scroll', () => {
      const header = document.getElementById('header');
      if (window.scrollY > 10) {
        header.classList.add('shadow-md');
        header.classList.remove('shadow-sm');
      } else {
        header.classList.remove('shadow-md');
        header.classList.add('shadow-sm');
      }
    });
  </script>
</body>
</html>

