<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FoodItem;

class FoodItemSeeder extends Seeder
{
    public function run()
    {
        $items = [
            ['name' => 'Veg Masala Bun', 'type' => 'veg', 'description' => 'Soft bun filled with spicy mixed veg masala.'],
            ['name' => 'Paneer Tikka Bun', 'type' => 'veg', 'description' => 'Bun stuffed with smoky paneer tikka.'],
            ['name' => 'Garlic Breadsticks', 'type' => 'veg', 'description' => 'Soft buttery sticks with garlic and herbs.'],
            ['name' => 'Cheese Garlic Loaf', 'type' => 'veg', 'description' => 'Garlic loaf slice with cheese and herbs.'],
            ['name' => 'Veg Puff', 'type' => 'veg', 'description' => 'Crispy puff with spicy veggie stuffing.'],
            ['name' => 'Paneer Puff', 'type' => 'veg', 'description' => 'Crispy puff topped with paneer and veggies.'],
            ['name' => 'Spinach & Corn Puff', 'type' => 'veg', 'description' => 'Crispy puff with creamy spinach-corn.'],
            ['name' => 'Mini Veg Pizza', 'type' => 'veg', 'description' => 'Soft crust pizza with cheese and veggies.'],
            ['name' => 'Stuffed Croissants', 'type' => 'veg', 'description' => 'Croissants with veggies or cheese filling.'],
            ['name' => 'Veg Fried Rice', 'type' => 'veg', 'description' => 'Rice tossed with veggies and spices.'],
            ['name' => 'Schezwan Fried Rice', 'type' => 'veg', 'description' => 'Rice with fiery schezwan sauce.'],
            ['name' => 'Veg Hakka Noodles', 'type' => 'veg', 'description' => 'Stir-fried noodles with vegetables.'],
            ['name' => 'Veg Garlic Noodles', 'type' => 'veg', 'description' => 'Garlic-flavored noodles with veggies.'],
            ['name' => 'Rajma-Chawal Bowl', 'type' => 'veg', 'description' => 'Rajma curry with rice.'],
            ['name' => 'Paneer Masala with Rice', 'type' => 'veg', 'description' => 'Paneer curry with steamed rice.'],
            ['name' => 'Chole-Chawal Bowl', 'type' => 'veg', 'description' => 'Punjabi chole with rice.'],
            ['name' => 'Aloo Paratha', 'type' => 'veg', 'description' => 'Stuffed paratha with spicy potatoes.'],
            ['name' => 'Paneer Paratha', 'type' => 'veg', 'description' => 'Stuffed paratha with spicy paneer.'],
            ['name' => 'Lachha Paratha', 'type' => 'veg', 'description' => 'Layered paratha cooked in ghee.'],
            ['name' => 'Sattu Paratha', 'type' => 'veg', 'description' => 'Paratha stuffed with sattu.'],
            ['name' => 'Veg Pakoras', 'type' => 'veg', 'description' => 'Crispy fritters of vegetables.'],
            ['name' => 'Crispy Corn', 'type' => 'veg', 'description' => 'Golden fried corn with spices.'],
            ['name' => 'Cheese Corn Balls', 'type' => 'veg', 'description' => 'Cheesy and crispy corn balls.'],
            ['name' => 'French Fries', 'type' => 'veg', 'description' => 'Classic golden fries.'],
            ['name' => 'Masala Potato Wedges', 'type' => 'veg', 'description' => 'Spicy potato wedges.'],
            ['name' => 'White Wonder Pasta', 'type' => 'veg', 'description' => 'Creamy white sauce pasta.'],
            ['name' => 'Red Fusion Pasta', 'type' => 'veg', 'description' => 'Tangy red sauce pasta.'],
            ['name' => 'Classic Maggi', 'type' => 'veg', 'description' => 'Maggi with spices and veggies.'],
            ['name' => 'Cheese Maggi', 'type' => 'veg', 'description' => 'Cheesy Maggi noodles.'],
            ['name' => 'Veg Momos (Steamed)', 'type' => 'veg', 'description' => 'Steamed dumplings with veggies.'],
            ['name' => 'Veg Momos (Fried)', 'type' => 'veg', 'description' => 'Fried dumplings with veggies.'],
            ['name' => 'Schezwan Maggi', 'type' => 'veg', 'description' => 'Spicy schezwan-flavored Maggi noodles.'],
            ['name' => 'Tandoori Cheese Maggi', 'type' => 'veg', 'description' => 'Maggi cooked with tandoori cheese masala.'],
            ['name' => 'Peri Peri Maggi', 'type' => 'veg', 'description' => 'Maggi with peri peri seasoning.'],
            ['name' => 'Chocolate Brownie', 'type' => 'veg', 'description' => 'Soft, rich chocolate brownie.'],
            ['name' => 'Chocolate Lava Cake', 'type' => 'veg', 'description' => 'Molten lava cake with rich chocolate center.'],
            ['name' => 'Oreo Shake', 'type' => 'veg', 'description' => 'Thick shake with crushed Oreos.'],
            ['name' => 'KitKat Shake', 'type' => 'veg', 'description' => 'Chocolate shake blended with KitKat.'],
            ['name' => 'Cold Coffee', 'type' => 'veg', 'description' => 'Classic iced coffee with milk.'],
            ['name' => 'Iced Tea (Lemon)', 'type' => 'veg', 'description' => 'Chilled lemon flavored iced tea.'],
            ['name' => 'Iced Tea (Peach)', 'type' => 'veg', 'description' => 'Refreshing peach flavored iced tea.'],
            ['name' => 'Lemon Mojito', 'type' => 'veg', 'description' => 'Zesty lemon soda with mint.'],
            ['name' => 'Blue Lagoon', 'type' => 'veg', 'description' => 'Refreshing blue mocktail with citrus notes.'],
            ['name' => 'Strawberry Mocktail', 'type' => 'veg', 'description' => 'Mocktail with strawberry flavor.'],
            ['name' => 'Chocolate Smoothie', 'type' => 'veg', 'description' => 'Smoothie with rich chocolate and milk.'],
            ['name' => 'Banana Smoothie', 'type' => 'veg', 'description' => 'Healthy banana and milk smoothie.'],
            ['name' => 'Cheese Burst Sandwich', 'type' => 'veg', 'description' => 'Sandwich oozing with cheese.'],
            ['name' => 'Tandoori Paneer Sandwich', 'type' => 'veg', 'description' => 'Sandwich with tandoori paneer filling.'],
            ['name' => 'Veg Club Sandwich', 'type' => 'veg', 'description' => 'Layered sandwich with veggies & cheese.'],
            ['name' => 'Corn & Mayo Sandwich', 'type' => 'veg', 'description' => 'Creamy corn-mayo stuffed sandwich.'],
            ['name' => 'Aloo Tikki Burger', 'type' => 'veg', 'description' => 'Burger with crispy potato patty.'],
            ['name' => 'Cheese Aloo Tikki Burger', 'type' => 'veg', 'description' => 'Cheesy version of Aloo Tikki Burger.'],
            ['name' => 'Paneer Tikka Burger', 'type' => 'veg', 'description' => 'Burger with tandoori paneer patty.'],
            ['name' => 'Crispy Veg Burger', 'type' => 'veg', 'description' => 'Burger with crispy vegetable patty.'],
            ['name' => 'Veg Cutlet', 'type' => 'veg', 'description' => 'Pan-fried veg cutlets.'],
            ['name' => 'Stuffed Kulcha', 'type' => 'veg', 'description' => 'Kulcha stuffed with spicy filling.'],
            ['name' => 'Cheesy Nachos', 'type' => 'veg', 'description' => 'Crispy nachos topped with cheese.'],
            ['name' => 'Nachos with Salsa', 'type' => 'veg', 'description' => 'Nachos served with spicy salsa dip.'],
            ['name' => 'Tandoori Garlic Bread', 'type' => 'veg', 'description' => 'Garlic bread with tandoori spices.'],
            ['name' => 'Paneer Rolls', 'type' => 'veg', 'description' => 'Wraps filled with spiced paneer.'],
            ['name' => 'Veg Rolls', 'type' => 'veg', 'description' => 'Wraps with spiced veggies.'],
        ];

        foreach ($items as $item) {
            FoodItem::create($item + ['mood_id' => 1]); // default mood_id for now
        }
    }
}

