import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { foodItemsAPI, moodsAPI } from '../services/api';
import GlobalHeader from '../components/GlobalHeader';
import GlobalFooter from '../components/GlobalFooter';

// Add CSS for animations and hover effects
const styles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .food-card {
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  }

  .food-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .food-info {
    transition: opacity 0.3s ease-in-out;
  }

  .mood-tab {
    transition: all 0.3s ease-in-out;
  }

  .mood-tab:hover:not(.active) {
    background-color: rgba(255, 111, 97, 0.1);
  }

  @media (min-width: 768px) {
    .md\\:flex {
      display: flex !important;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

function FoodMenu() {
  const navigate = useNavigate();
  const [foodItems, setFoodItems] = useState([]);
  const [moods, setMoods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedMood, setSelectedMood] = useState('comfort');
  const [searchTerm, setSearchTerm] = useState('');
  const [userName, setUserName] = useState('User');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedSort, setSelectedSort] = useState('Sort by: Recommended');
  const [selectedDietaryFilter, setSelectedDietaryFilter] = useState('All');

  // Mood categories for filtering
  const moodCategories = [
    { id: 'comfort', name: 'Comfort', icon: 'fa-house-heart', color: '#FF6F61' },
    { id: 'energy', name: 'Energy', icon: 'fa-bolt', color: '#FFF3B0' },
    { id: 'calm', name: 'Calm', icon: 'fa-peace', color: '#83C5BE' },
    { id: 'focus', name: 'Focus', icon: 'fa-brain', color: '#006D77' },
    { id: 'joy', name: 'Joy', icon: 'fa-face-smile', color: '#FFDAB9' }
  ];

  // Sample food data (will be replaced with API data)
  const sampleFoodItems = [
    {
      id: 1,
      name: 'Rainbow Buddha Bowl',
      price: 14.95,
      description: 'A vibrant mix of roasted vegetables, fresh greens, and protein-packed quinoa with our signature tahini dressing.',
      image: 'https://storage.googleapis.com/uxpilot-auth.appspot.com/96c3a4e649-a2c0c47540d85c868ae3.png',
      moods: ['joy', 'energy'],
      tags: ['Vegan', 'Gluten-Free', 'High-Protein'],
      category: 'Bowls & Salads'
    },
    {
      id: 2,
      name: 'Butternut Bliss Soup',
      price: 11.50,
      description: 'Velvety butternut squash soup with aromatic herbs and crunchy sourdough croutons. Pure comfort in a bowl.',
      image: 'https://storage.googleapis.com/uxpilot-auth.appspot.com/0c055abc93-4e1970e151def2694c60.png',
      moods: ['comfort', 'calm'],
      tags: ['Vegetarian', 'Gluten-Free Option'],
      category: 'Soups & Stews'
    },
    {
      id: 3,
      name: 'Matcha Energy Bowl',
      price: 13.75,
      description: 'Antioxidant-rich matcha blended with banana and topped with seasonal berries, hemp seeds, and house-made granola.',
      image: 'https://storage.googleapis.com/uxpilot-auth.appspot.com/301d1175a5-7d75aa4c46093172ac33.png',
      moods: ['focus', 'energy'],
      tags: ['Vegan', 'Antioxidant-Rich'],
      category: 'Breakfast'
    },
    {
      id: 4,
      name: 'Wild Mushroom Risotto',
      price: 16.95,
      description: 'Creamy arborio rice with wild mushrooms, finished with aged parmesan and fresh herbs.',
      image: 'https://storage.googleapis.com/uxpilot-auth.appspot.com/b6b990e147-015d3686bfd37647d588.png',
      moods: ['comfort'],
      tags: ['Vegetarian', 'Award-Winning'],
      category: 'Mains'
    },
    {
      id: 5,
      name: 'Green Thai Curry',
      price: 15.50,
      description: 'Aromatic Thai green curry with crispy tofu, seasonal vegetables, and fragrant jasmine rice.',
      image: 'https://storage.googleapis.com/uxpilot-auth.appspot.com/88918df2bd-3460051e304ef1f50f64.png',
      moods: ['joy'],
      tags: ['Vegan', 'Gluten-Free', 'Spicy'],
      category: 'Mains'
    },
    {
      id: 6,
      name: 'Artisanal Avocado Toast',
      price: 12.75,
      description: 'Crusty sourdough topped with smashed avocado, perfectly poached egg, and delicate microgreens.',
      image: 'https://storage.googleapis.com/uxpilot-auth.appspot.com/f36500d799-c522bf3fb07474157133.png',
      moods: ['energy'],
      tags: ['Vegetarian', 'High-Protein'],
      category: 'Breakfast'
    }
  ];

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }

    // For now, use sample data. Later can be replaced with API calls
    setFoodItems(sampleFoodItems);
    setLoading(false);
  }, []);

  const handleMoodTabClick = (moodId) => {
    setSelectedMood(moodId);
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleDietaryFilterClick = (filter) => {
    setSelectedDietaryFilter(filter);
  };

  // Filter food items based on search term, mood, and dietary preferences
  const filteredFoodItems = foodItems.filter(foodItem => {
    const matchesSearch = !searchTerm ||
                         foodItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         foodItem.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         foodItem.category.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesMood = selectedMood === 'all' || foodItem.moods.includes(selectedMood);

    const matchesDietary = selectedDietaryFilter === 'All' ||
                          foodItem.tags.some(tag => tag.toLowerCase().includes(selectedDietaryFilter.toLowerCase()));

    return matchesSearch && matchesMood && matchesDietary;
  });

  const recommendedItems = sampleFoodItems.slice(0, 3); // First 3 items as recommended

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      {/* Global Header */}
      <GlobalHeader />

      {/* Hero Section */}
      <section style={{
        position: 'relative',
        height: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          zIndex: 10
        }}></div>
        <img
          style={{
            position: 'absolute',
            inset: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
          src="https://storage.googleapis.com/uxpilot-auth.appspot.com/bac237752c-10da0d2f69d679c96006.png"
          alt="beautiful vegetarian food spread on a table, artistic food photography, moody lighting, high quality"
        />

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 16px',
          position: 'relative',
          zIndex: 20,
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '48px',
            fontFamily: 'Poppins, sans-serif',
            fontWeight: 'bold',
            color: 'white',
            marginBottom: '24px'
          }}>Curated for Your Mood</h1>
          <p style={{
            fontSize: '18px',
            color: 'rgba(255, 255, 255, 0.9)',
            marginBottom: '32px',
            maxWidth: '800px',
            margin: '0 auto 32px'
          }}>Discover vegetarian dishes that nourish both body and mind, perfectly matched to how you feel today.</p>

          {/* Search Bar */}
          <div style={{
            position: 'relative',
            maxWidth: '800px',
            margin: '0 auto 32px'
          }}>
            <input
              type="text"
              placeholder="Search for dishes, ingredients, or moods..."
              value={searchTerm}
              onChange={handleSearch}
              style={{
                width: '100%',
                padding: '16px 24px',
                borderRadius: '50px',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                color: '#1f2937',
                border: 'none',
                outline: 'none',
                fontSize: '16px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
            <button style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              backgroundColor: '#FF6F61',
              color: 'white',
              padding: '12px',
              borderRadius: '50%',
              border: 'none',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#006D77'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}>
              <i className="fa-solid fa-search"></i>
            </button>
          </div>

          {/* Mood Tabs */}
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            gap: '8px'
          }}>
            {moodCategories.map((mood) => (
              <button
                key={mood.id}
                onClick={() => handleMoodTabClick(mood.id)}
                style={{
                  backgroundColor: selectedMood === mood.id ? '#FF6F61' : 'rgba(255, 255, 255, 0.9)',
                  color: selectedMood === mood.id ? 'white' : '#374151',
                  padding: '8px 24px',
                  borderRadius: '50px',
                  fontWeight: '500',
                  fontSize: '14px',
                  border: 'none',
                  cursor: 'pointer',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s'
                }}
                onMouseEnter={(e) => {
                  if (selectedMood !== mood.id) {
                    e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedMood !== mood.id) {
                    e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                  }
                }}
              >
                <i className={`fa-solid ${mood.icon}`} style={{ marginRight: '8px' }}></i>
                {mood.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Recommended Section */}
      <section style={{ padding: '48px 0', backgroundColor: 'white' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 16px' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '32px'
          }}>
            <h2 style={{
              fontSize: '32px',
              fontFamily: 'Poppins, sans-serif',
              fontWeight: 'bold',
              color: '#1f2937'
            }}>Recommended for You</h2>
            <span style={{
              color: '#FF6F61',
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              transition: 'color 0.3s'
            }}
            onMouseEnter={(e) => e.target.style.color = '#006D77'}
            onMouseLeave={(e) => e.target.style.color = '#FF6F61'}>
              <span>View all</span>
              <i className="fa-solid fa-arrow-right" style={{ marginLeft: '8px' }}></i>
            </span>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '24px'
          }}>
            {recommendedItems.map((item) => (
              <div
                key={item.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                }}
              >
                <div style={{ position: 'relative', height: '224px' }}>
                  <img
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    src={item.image}
                    alt={item.name}
                  />
                  <div style={{
                    position: 'absolute',
                    top: '12px',
                    right: '12px',
                    display: 'flex',
                    gap: '8px'
                  }}>
                    {item.moods.map((mood) => {
                      const moodData = moodCategories.find(m => m.id === mood);
                      return (
                        <span
                          key={mood}
                          style={{
                            backgroundColor: `${moodData?.color || '#6b7280'}E6`,
                            color: '#006D77',
                            fontSize: '12px',
                            padding: '4px 8px',
                            borderRadius: '50px',
                            fontWeight: '500'
                          }}
                        >
                          <i className={`fa-solid ${moodData?.icon || 'fa-circle'}`} style={{ marginRight: '4px' }}></i>
                          {moodData?.name || mood}
                        </span>
                      );
                    })}
                  </div>
                </div>
                <div style={{ padding: '20px' }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '8px'
                  }}>
                    <h3 style={{
                      fontSize: '20px',
                      fontWeight: '600',
                      color: '#1f2937'
                    }}>{item.name}</h3>
                    <span style={{
                      color: '#FF6F61',
                      fontWeight: 'bold'
                    }}>${item.price}</span>
                  </div>
                  <p style={{
                    color: '#6b7280',
                    fontSize: '14px',
                    marginBottom: '16px'
                  }}>{item.description}</p>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    marginBottom: '16px'
                  }}>
                    {item.tags.map((tag) => (
                      <span
                        key={tag}
                        style={{
                          backgroundColor: tag.includes('Vegan') ? '#dcfce7' :
                                          tag.includes('Gluten') ? '#fef3c7' : '#dbeafe',
                          color: tag.includes('Vegan') ? '#166534' :
                                tag.includes('Gluten') ? '#92400e' : '#1e40af',
                          fontSize: '12px',
                          padding: '4px 8px',
                          borderRadius: '50px'
                        }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <button style={{
                      flex: 1,
                      backgroundColor: '#FF6F61',
                      color: 'white',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      border: 'none',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: '500',
                      transition: 'background-color 0.3s'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#006D77'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}>
                      Add to Cart
                    </button>
                    <button style={{
                      backgroundColor: '#f3f4f6',
                      color: '#374151',
                      padding: '8px',
                      borderRadius: '8px',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#e5e7eb'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#f3f4f6'}>
                      <i className="fa-regular fa-heart"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* Menu Filter Section */}
      <section style={{ padding: '32px 0', backgroundColor: 'rgba(245, 232, 199, 0.5)' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 16px' }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '32px',
            gap: '16px'
          }} className="md:flex-row md:items-center">
            <h2 style={{
              fontSize: '32px',
              fontFamily: 'Poppins, sans-serif',
              fontWeight: 'bold',
              color: '#1f2937'
            }}>Explore Our Menu</h2>

            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px' }}>
              <div style={{ position: 'relative' }}>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  style={{
                    appearance: 'none',
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    color: '#374151',
                    padding: '8px 32px 8px 16px',
                    borderRadius: '8px',
                    outline: 'none',
                    cursor: 'pointer'
                  }}
                >
                  <option>All Categories</option>
                  <option>Bowls & Salads</option>
                  <option>Soups & Stews</option>
                  <option>Breakfast</option>
                  <option>Mains</option>
                  <option>Desserts</option>
                </select>
                <div style={{
                  pointerEvents: 'none',
                  position: 'absolute',
                  top: '50%',
                  right: '8px',
                  transform: 'translateY(-50%)',
                  color: '#374151'
                }}>
                  <i className="fa-solid fa-chevron-down" style={{ fontSize: '12px' }}></i>
                </div>
              </div>

              <div style={{ position: 'relative' }}>
                <select
                  value={selectedSort}
                  onChange={(e) => setSelectedSort(e.target.value)}
                  style={{
                    appearance: 'none',
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    color: '#374151',
                    padding: '8px 32px 8px 16px',
                    borderRadius: '8px',
                    outline: 'none',
                    cursor: 'pointer'
                  }}
                >
                  <option>Sort by: Recommended</option>
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                  <option>Most Popular</option>
                  <option>Mood Match</option>
                </select>
                <div style={{
                  pointerEvents: 'none',
                  position: 'absolute',
                  top: '50%',
                  right: '8px',
                  transform: 'translateY(-50%)',
                  color: '#374151'
                }}>
                  <i className="fa-solid fa-chevron-down" style={{ fontSize: '12px' }}></i>
                </div>
              </div>

              <button style={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                color: '#374151',
                padding: '8px 16px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'background-color 0.3s'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}>
                <i className="fa-solid fa-sliders" style={{ marginRight: '8px' }}></i>Filters
              </button>
            </div>
          </div>

          {/* Dietary Filters */}
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px', marginBottom: '32px' }}>
            {['All', 'Vegan', 'Gluten-Free', 'Organic', 'Spicy', 'Protein-Rich'].map((filter) => (
              <button
                key={filter}
                onClick={() => handleDietaryFilterClick(filter)}
                style={{
                  backgroundColor: selectedDietaryFilter === filter ? 'rgba(255, 111, 97, 0.1)' : 'white',
                  border: selectedDietaryFilter === filter ? '1px solid rgba(255, 111, 97, 0.2)' : '1px solid #e5e7eb',
                  color: selectedDietaryFilter === filter ? '#FF6F61' : '#374151',
                  padding: '4px 16px',
                  borderRadius: '50px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.3s'
                }}
                onMouseEnter={(e) => {
                  if (selectedDietaryFilter !== filter) {
                    e.target.style.backgroundColor = '#f9fafb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedDietaryFilter !== filter) {
                    e.target.style.backgroundColor = 'white';
                  }
                }}
              >
                {filter === 'Vegan' && <i className="fa-solid fa-leaf" style={{ marginRight: '4px', color: '#059669' }}></i>}
                {filter === 'Gluten-Free' && <i className="fa-solid fa-wheat-awn-circle-exclamation" style={{ marginRight: '4px', color: '#d97706' }}></i>}
                {filter === 'Organic' && <i className="fa-solid fa-seedling" style={{ marginRight: '4px', color: '#10b981' }}></i>}
                {filter === 'Spicy' && <i className="fa-solid fa-fire-flame-simple" style={{ marginRight: '4px', color: '#f97316' }}></i>}
                {filter === 'Protein-Rich' && <i className="fa-solid fa-dumbbell" style={{ marginRight: '4px', color: '#374151' }}></i>}
                {filter}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Menu Grid Section */}
      <section style={{ padding: '48px 0', backgroundColor: 'rgba(245, 232, 199, 0.5)' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 16px' }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '24px'
          }}>
            {filteredFoodItems.map((item) => (
              <div
                key={item.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                }}
              >
                <div style={{ position: 'relative', height: '256px' }}>
                  <img
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    src={item.image}
                    alt={item.name}
                  />
                  <div style={{
                    position: 'absolute',
                    top: '12px',
                    right: '12px',
                    display: 'flex',
                    gap: '8px'
                  }}>
                    {item.moods.map((mood) => {
                      const moodData = moodCategories.find(m => m.id === mood);
                      return (
                        <span
                          key={mood}
                          style={{
                            backgroundColor: `${moodData?.color || '#6b7280'}E6`,
                            color: '#006D77',
                            fontSize: '12px',
                            padding: '4px 8px',
                            borderRadius: '50px',
                            fontWeight: '500'
                          }}
                        >
                          <i className={`fa-solid ${moodData?.icon || 'fa-circle'}`} style={{ marginRight: '4px' }}></i>
                          {moodData?.name || mood}
                        </span>
                      );
                    })}
                  </div>
                  <div style={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    padding: '16px',
                    background: 'linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent)',
                    color: 'white',
                    opacity: 0,
                    transition: 'opacity 0.3s'
                  }}
                  className="food-info"
                  onMouseEnter={(e) => e.currentTarget.style.opacity = 1}
                  onMouseLeave={(e) => e.currentTarget.style.opacity = 0}>
                    <h4 style={{ fontWeight: '500', marginBottom: '4px' }}>Ingredients:</h4>
                    <p style={{ fontSize: '12px' }}>Fresh seasonal ingredients, carefully selected for quality and flavor</p>
                  </div>
                </div>
                <div style={{ padding: '20px' }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '8px'
                  }}>
                    <h3 style={{
                      fontSize: '20px',
                      fontWeight: '600',
                      color: '#1f2937'
                    }}>{item.name}</h3>
                    <span style={{
                      color: '#FF6F61',
                      fontWeight: 'bold'
                    }}>${item.price}</span>
                  </div>
                  <p style={{
                    color: '#6b7280',
                    fontSize: '14px',
                    marginBottom: '16px'
                  }}>{item.description}</p>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    marginBottom: '16px'
                  }}>
                    {item.tags.map((tag) => (
                      <span
                        key={tag}
                        style={{
                          backgroundColor: tag.includes('Vegan') ? '#dcfce7' :
                                          tag.includes('Gluten') ? '#fef3c7' : '#dbeafe',
                          color: tag.includes('Vegan') ? '#166534' :
                                tag.includes('Gluten') ? '#92400e' : '#1e40af',
                          fontSize: '12px',
                          padding: '4px 8px',
                          borderRadius: '50px'
                        }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <button style={{
                      flex: 1,
                      backgroundColor: '#FF6F61',
                      color: 'white',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      border: 'none',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: '500',
                      transition: 'background-color 0.3s'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#006D77'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}>
                      Add to Cart
                    </button>
                    <button style={{
                      backgroundColor: '#f3f4f6',
                      color: '#374151',
                      padding: '8px',
                      borderRadius: '8px',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#e5e7eb'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#f3f4f6'}>
                      <i className="fa-regular fa-heart"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More Button */}
          <div style={{ textAlign: 'center', marginTop: '48px' }}>
            <button style={{
              backgroundColor: '#006D77',
              color: 'white',
              padding: '12px 32px',
              borderRadius: '8px',
              border: 'none',
              cursor: 'pointer',
              fontWeight: '500',
              transition: 'background-color 0.3s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.9)'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#006D77'}>
              Load More Dishes
            </button>
          </div>
        </div>
      </section>

      {/* Global Footer */}
      <GlobalFooter />
    </div>
  );
}

export default FoodMenu;
