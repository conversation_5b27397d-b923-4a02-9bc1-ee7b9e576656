<?php

namespace App\Http\Controllers\API;

use App\Models\SubMood;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SubMoodController extends Controller
{
    public function index()
    {
        return response()->json(SubMood::with('mood')->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'mood_id' => 'required|exists:moods,id',
            'name' => 'required|string',
            'meaning' => 'nullable|string',
        ]);

        $subMood = SubMood::create($request->all());
        return response()->json($subMood, 201);
    }

    public function show($id)
    {
        return response()->json(SubMood::with('mood')->findOrFail($id));
    }

    public function update(Request $request, $id)
    {
        $subMood = SubMood::findOrFail($id);
        $subMood->update($request->all());
        return response()->json($subMood);
    }

    public function destroy($id)
    {
        SubMood::findOrFail($id)->delete();
        return response()->json(['message' => 'Sub-mood deleted']);
    }
}

