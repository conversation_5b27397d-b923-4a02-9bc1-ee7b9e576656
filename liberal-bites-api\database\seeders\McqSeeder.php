<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\McqQuestion;
use App\Models\McqOption;

class Mcq<PERSON>eed<PERSON> extends Seeder
{
    public function run()
    {
        for ($i = 1; $i <= 30; $i++) {
            $question = McqQuestion::create([
                'question' => "Sample Question $i: What do you feel about topic $i?"
            ]);

            $options = [
                ['option_text' => 'Strongly Disagree', 'points' => 1],
                ['option_text' => 'Disagree', 'points' => 2],
                ['option_text' => 'Agree', 'points' => 3],
                ['option_text' => 'Strongly Agree', 'points' => 4],
            ];

            foreach ($options as $opt) {
                McqOption::create([
                    'question_id' => $question->id,
                    'option_text' => $opt['option_text'],
                    'points'      => $opt['points'],
                ]);
            }
        }
    }
}

