import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardSidebar from '../../components/DashboardSidebar';

function AdminDashboard() {
  const navigate = useNavigate();
  const [showSidebar, setShowSidebar] = useState(false);
  const [userName, setUserName] = useState('Aisha');

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    }
  }, []);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      <style>
        {`
          .hover\\:bg-gray-100:hover {
            background-color: #f3f4f6;
          }
          @media (min-width: 768px) {
            .md\\:translate-x-0 {
              transform: translateX(0) !important;
            }
          }
        `}
      </style>

      {/* Dashboard Sidebar */}
      <DashboardSidebar />

      {/* Main Content Area */}
      <div style={{
        paddingTop: '0px',
        paddingLeft: window.innerWidth >= 768 ? '256px' : '0',
        minHeight: 'calc(100vh - 140px)'
      }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto', padding: '16px 24px' }}>
          {/* Welcome Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth >= 768 ? '2fr 1fr' : '1fr',
            gap: '24px',
            marginBottom: '24px'
          }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h1 style={{
                  fontSize: window.innerWidth >= 768 ? '30px' : '24px',
                  fontWeight: 'bold',
                  color: '#1f2937'
                }}>Good afternoon, {userName}!</h1>
                <p style={{ color: '#4b5563', marginTop: '4px' }}>Ready to discover your perfect meal match?</p>

                <div style={{ marginTop: '16px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                    <span style={{ fontSize: '14px', color: '#4b5563' }}>Weekly mood tracking</span>
                    <span style={{ fontSize: '14px', fontWeight: '500', color: '#006D77' }}>5/7 days</span>
                  </div>
                  <div style={{
                    width: '100%',
                    backgroundColor: '#e5e7eb',
                    borderRadius: '9999px',
                    height: '10px'
                  }}>
                    <div style={{
                      backgroundColor: '#006D77',
                      height: '10px',
                      borderRadius: '9999px',
                      width: '71%'
                    }}></div>
                  </div>
                </div>

                <div style={{ marginTop: '16px', display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                  <span style={{
                    padding: '4px 12px',
                    backgroundColor: 'rgba(0, 109, 119, 0.1)',
                    color: '#006D77',
                    borderRadius: '9999px',
                    fontSize: '14px'
                  }}>Calm</span>
                  <span style={{
                    padding: '4px 12px',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    color: '#1d4ed8',
                    borderRadius: '9999px',
                    fontSize: '14px'
                  }}>Focused</span>
                  <span style={{
                    padding: '4px 12px',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    color: '#d97706',
                    borderRadius: '9999px',
                    fontSize: '14px'
                  }}>Comfort-seeking</span>
                  <span style={{
                    padding: '4px 12px',
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    color: '#7c3aed',
                    borderRadius: '9999px',
                    fontSize: '14px'
                  }}>Reflective</span>
                </div>
              </div>
            </div>

            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>Quick Actions</h2>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  <button style={{
                    width: '100%',
                    padding: '10px 16px',
                    backgroundColor: '#FF6F61',
                    color: 'white',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background-color 0.3s ease'
                  }} onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                     onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}>
                    <i className="fa-solid fa-bolt" style={{ marginRight: '8px' }}></i>
                    <span>Feeling Lucky</span>
                  </button>
                  <button style={{
                    width: '100%',
                    padding: '10px 16px',
                    border: '1px solid #006D77',
                    color: '#006D77',
                    backgroundColor: 'transparent',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background-color 0.3s ease'
                  }} onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.05)'}
                     onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
                    <i className="fa-solid fa-rotate-left" style={{ marginRight: '8px' }}></i>
                    <span>Repeat Last Order</span>
                  </button>
                  <button style={{
                    width: '100%',
                    padding: '10px 16px',
                    color: '#374151',
                    backgroundColor: 'transparent',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background-color 0.3s ease'
                  }} onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
                     onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
                    <i className="fa-solid fa-history" style={{ marginRight: '8px' }}></i>
                    <span>View History</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Core Action Cards Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth >= 768 ? '1fr 1fr' : '1fr',
            gap: '24px',
            marginBottom: '32px'
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
              transition: 'box-shadow 0.3s ease'
            }} onMouseEnter={(e) => e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'}
               onMouseLeave={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}>
              <div style={{
                height: '192px',
                borderRadius: '12px 12px 0 0',
                backgroundImage: 'url("https://images.unsplash.com/photo-1499209974431-9dddcece7f88?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80")',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  inset: 0,
                  background: 'linear-gradient(to top, rgba(0,0,0,0.6), transparent)',
                  borderRadius: '12px 12px 0 0'
                }}></div>
                <div style={{
                  position: 'absolute',
                  bottom: '16px',
                  left: '24px',
                  color: 'white'
                }}>
                  <h3 style={{ fontSize: '20px', fontWeight: 'bold' }}>Discover Through Your Mood</h3>
                </div>
              </div>
              <div style={{ padding: '24px' }}>
                <p style={{ color: '#4b5563', marginBottom: '16px' }}>
                  Take our 5-minute personalized assessment to find the perfect food and book matches for your current emotional state.
                </p>

                <ul style={{ listStyle: 'none', padding: 0, margin: 0, marginBottom: '16px' }}>
                  <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '8px' }}>
                    <i className="fa-solid fa-sparkles" style={{ color: '#FF6F61', marginTop: '4px', width: '20px' }}></i>
                    <span style={{ marginLeft: '8px' }}>100+ emotional states analyzed</span>
                  </li>
                  <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '8px' }}>
                    <i className="fa-solid fa-book" style={{ color: '#FF6F61', marginTop: '4px', width: '20px' }}></i>
                    <span style={{ marginLeft: '8px' }}>Matching book recommendations</span>
                  </li>
                  <li style={{ display: 'flex', alignItems: 'flex-start' }}>
                    <i className="fa-solid fa-bullseye" style={{ color: '#FF6F61', marginTop: '4px', width: '20px' }}></i>
                    <span style={{ marginLeft: '8px' }}>Personalized results just for you</span>
                  </li>
                </ul>

                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: '16px' }}>
                  <button style={{
                    padding: '10px 24px',
                    backgroundColor: '#FF6F61',
                    color: 'white',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    transition: 'background-color 0.3s ease'
                  }} onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                     onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                     onClick={() => navigate('/quiz')}>
                    Start Mood Quiz
                  </button>
                  <span style={{
                    padding: '4px 12px',
                    backgroundColor: '#f3f4f6',
                    color: '#374151',
                    borderRadius: '9999px',
                    fontSize: '14px'
                  }}>Last result: Comfort Food</span>
                </div>
              </div>
            </div>

            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
              transition: 'box-shadow 0.3s ease'
            }} onMouseEnter={(e) => e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'}
               onMouseLeave={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}>
              <div style={{
                height: '192px',
                borderRadius: '12px 12px 0 0',
                backgroundImage: 'url("https://images.unsplash.com/photo-1567337710282-00832b415979?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80")',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  inset: 0,
                  background: 'linear-gradient(to top, rgba(0,0,0,0.6), transparent)',
                  borderRadius: '12px 12px 0 0'
                }}></div>
                <div style={{
                  position: 'absolute',
                  bottom: '16px',
                  left: '24px',
                  color: 'white'
                }}>
                  <h3 style={{ fontSize: '20px', fontWeight: 'bold' }}>Browse Our Menu</h3>
                </div>
              </div>
              <div style={{ padding: '24px' }}>
                <p style={{ color: '#4b5563', marginBottom: '16px' }}>
                  Explore curated vegetarian comfort foods that nourish both body and mind, each tagged with emotional benefits.
                </p>

                <ul style={{ listStyle: 'none', padding: 0, margin: 0, marginBottom: '16px' }}>
                  <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '8px' }}>
                    <i className="fa-solid fa-seedling" style={{ color: '#006D77', marginTop: '4px', width: '20px' }}></i>
                    <span style={{ marginLeft: '8px' }}>100% vegetarian cuisine</span>
                  </li>
                  <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '8px' }}>
                    <i className="fa-solid fa-heart" style={{ color: '#006D77', marginTop: '4px', width: '20px' }}></i>
                    <span style={{ marginLeft: '8px' }}>Comfort food specialists</span>
                  </li>
                  <li style={{ display: 'flex', alignItems: 'flex-start' }}>
                    <i className="fa-solid fa-tag" style={{ color: '#006D77', marginTop: '4px', width: '20px' }}></i>
                    <span style={{ marginLeft: '8px' }}>Mood-tagged recipes</span>
                  </li>
                </ul>

                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: '16px' }}>
                  <button style={{
                    padding: '10px 24px',
                    border: '1px solid #006D77',
                    color: '#006D77',
                    backgroundColor: 'transparent',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'background-color 0.3s ease'
                  }} onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.05)'}
                     onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                     onClick={() => navigate('/food-menu')}>
                    Explore Menu
                  </button>
                  <span style={{
                    padding: '4px 12px',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    color: '#d97706',
                    borderRadius: '9999px',
                    fontSize: '14px'
                  }}>Popular: Rajma-Chawal Bowl</span>
                </div>
              </div>
            </div>
          </div>

          {/* Personal Insights Section */}
          <div style={{ marginBottom: '32px' }}>
            <div style={{
              background: 'linear-gradient(to right, #006D77, #FF6F61)',
              borderRadius: '12px',
              padding: '24px'
            }}>
              <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', marginBottom: '16px' }}>
                Your Wellness Journey
              </h2>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                gap: '16px'
              }}>
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '16px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0, 109, 119, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-clipboard-check" style={{ color: '#006D77' }}></i>
                    </div>
                    <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#1f2937' }}>12</span>
                  </div>
                  <div style={{ fontSize: '14px', color: '#4b5563' }}>Quizzes Completed</div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '16px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 111, 97, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-face-smile" style={{ color: '#FF6F61' }}></i>
                    </div>
                    <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#1f2937' }}>8</span>
                  </div>
                  <div style={{ fontSize: '14px', color: '#4b5563' }}>Moods Tracked</div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '16px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(245, 158, 11, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-utensils" style={{ color: '#d97706' }}></i>
                    </div>
                    <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#1f2937' }}>24</span>
                  </div>
                  <div style={{ fontSize: '14px', color: '#4b5563' }}>Meals Enjoyed</div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '16px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(139, 92, 246, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-book-open" style={{ color: '#7c3aed' }}></i>
                    </div>
                    <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#1f2937' }}>5</span>
                  </div>
                  <div style={{ fontSize: '14px', color: '#4b5563' }}>Books Discovered</div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity & Trending Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth >= 768 ? '2fr 1fr' : '1fr',
            gap: '24px',
            marginBottom: '32px'
          }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '16px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <i className="fa-solid fa-clock-rotate-left" style={{ marginRight: '8px', color: '#006D77' }}></i>
                  Recent Activity
                </h2>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                  <div style={{ display: 'flex' }}>
                    <div style={{ flexShrink: 0, width: '48px', textAlign: 'center' }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        backgroundColor: 'rgba(255, 111, 97, 0.2)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto'
                      }}>
                        <i className="fa-solid fa-utensils" style={{ color: '#FF6F61' }}></i>
                      </div>
                      <div style={{
                        width: '2px',
                        height: '100%',
                        backgroundColor: '#e5e7eb',
                        margin: '8px auto 0'
                      }}></div>
                    </div>
                    <div style={{ flex: 1, marginLeft: '16px' }}>
                      <div style={{ fontSize: '14px', color: '#6b7280' }}>Yesterday</div>
                      <div style={{ fontWeight: '500' }}>Feeling Stressed → Rajma-Chawal Bowl</div>
                      <div style={{ fontSize: '14px', color: '#4b5563', marginTop: '4px' }}>Paired with 'Ikigai' summary</div>
                      <div style={{ marginTop: '8px' }}>
                        <span style={{
                          padding: '2px 8px',
                          backgroundColor: 'rgba(239, 68, 68, 0.1)',
                          color: '#dc2626',
                          borderRadius: '9999px',
                          fontSize: '12px'
                        }}>Stress Relief</span>
                        <span style={{
                          padding: '2px 8px',
                          backgroundColor: 'rgba(245, 158, 11, 0.1)',
                          color: '#d97706',
                          borderRadius: '9999px',
                          fontSize: '12px',
                          marginLeft: '4px'
                        }}>Comfort Food</span>
                      </div>
                    </div>
                  </div>

                  <div style={{ display: 'flex' }}>
                    <div style={{ flexShrink: 0, width: '48px', textAlign: 'center' }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        backgroundColor: 'rgba(0, 109, 119, 0.2)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto'
                      }}>
                        <i className="fa-solid fa-brain" style={{ color: '#006D77' }}></i>
                      </div>
                      <div style={{
                        width: '2px',
                        height: '100%',
                        backgroundColor: '#e5e7eb',
                        margin: '8px auto 0'
                      }}></div>
                    </div>
                    <div style={{ flex: 1, marginLeft: '16px' }}>
                      <div style={{ fontSize: '14px', color: '#6b7280' }}>2 days ago</div>
                      <div style={{ fontWeight: '500' }}>Overthinking → Tomato Soup</div>
                      <div style={{ fontSize: '14px', color: '#4b5563', marginTop: '4px' }}>Paired with 'The Power of Now'</div>
                      <div style={{ marginTop: '8px' }}>
                        <span style={{
                          padding: '2px 8px',
                          backgroundColor: 'rgba(59, 130, 246, 0.1)',
                          color: '#1d4ed8',
                          borderRadius: '9999px',
                          fontSize: '12px'
                        }}>Calming</span>
                        <span style={{
                          padding: '2px 8px',
                          backgroundColor: 'rgba(139, 92, 246, 0.1)',
                          color: '#7c3aed',
                          borderRadius: '9999px',
                          fontSize: '12px',
                          marginLeft: '4px'
                        }}>Mindfulness</span>
                      </div>
                    </div>
                  </div>

                  <div style={{ display: 'flex' }}>
                    <div style={{ flexShrink: 0, width: '48px', textAlign: 'center' }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto'
                      }}>
                        <i className="fa-solid fa-mug-hot" style={{ color: '#d97706' }}></i>
                      </div>
                    </div>
                    <div style={{ flex: 1, marginLeft: '16px' }}>
                      <div style={{ fontSize: '14px', color: '#6b7280' }}>Last week</div>
                      <div style={{ fontWeight: '500' }}>Lonely → Masala Milk Tea</div>
                      <div style={{ fontSize: '14px', color: '#4b5563', marginTop: '4px' }}>Paired with 'Tuesdays with Morrie'</div>
                      <div style={{ marginTop: '8px' }}>
                        <span style={{
                          padding: '2px 8px',
                          backgroundColor: 'rgba(34, 197, 94, 0.1)',
                          color: '#16a34a',
                          borderRadius: '9999px',
                          fontSize: '12px'
                        }}>Comforting</span>
                        <span style={{
                          padding: '2px 8px',
                          backgroundColor: 'rgba(99, 102, 241, 0.1)',
                          color: '#4f46e5',
                          borderRadius: '9999px',
                          fontSize: '12px',
                          marginLeft: '4px'
                        }}>Connection</span>
                      </div>
                    </div>
                  </div>
                </div>

                <button style={{
                  marginTop: '16px',
                  color: '#006D77',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '14px',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer'
                }}>
                  <span>View all activity</span>
                  <i className="fa-solid fa-chevron-right" style={{ marginLeft: '4px', fontSize: '12px' }}></i>
                </button>
              </div>
            </div>

            <div>
              <div style={{
                backgroundColor: 'rgba(0, 109, 119, 0.1)',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '16px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <i className="fa-solid fa-fire" style={{ marginRight: '8px', color: '#FF6F61' }}></i>
                  Trending Today
                </h2>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '8px',
                      backgroundImage: 'url("https://images.unsplash.com/photo-1631292784640-2b24be148025?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80")',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}></div>
                    <div style={{ marginLeft: '12px', flex: 1 }}>
                      <div style={{ fontWeight: '500' }}>Chana Masala Bowl</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>For: Motivation, Energy</div>
                    </div>
                    <button style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: 'rgba(255, 111, 97, 0.1)',
                      color: '#FF6F61',
                      borderRadius: '50%',
                      border: 'none',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-plus"></i>
                    </button>
                  </div>

                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '8px',
                      backgroundImage: 'url("https://images.unsplash.com/photo-1600271886742-f049cd451bba?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80")',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}></div>
                    <div style={{ marginLeft: '12px', flex: 1 }}>
                      <div style={{ fontWeight: '500' }}>Coconut Kheer</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>For: Comfort, Nostalgia</div>
                    </div>
                    <button style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: 'rgba(255, 111, 97, 0.1)',
                      color: '#FF6F61',
                      borderRadius: '50%',
                      border: 'none',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-plus"></i>
                    </button>
                  </div>

                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '8px',
                      backgroundImage: 'url("https://images.unsplash.com/photo-1563379926898-05f4575a45d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80")',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}></div>
                    <div style={{ marginLeft: '12px', flex: 1 }}>
                      <div style={{ fontWeight: '500' }}>Lemon Ginger Tea</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>For: Focus, Clarity</div>
                    </div>
                    <button style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: 'rgba(255, 111, 97, 0.1)',
                      color: '#FF6F61',
                      borderRadius: '50%',
                      border: 'none',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-plus"></i>
                    </button>
                  </div>

                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '8px',
                      backgroundImage: 'url("https://images.unsplash.com/photo-1547592180-85f173990554?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80")',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}></div>
                    <div style={{ marginLeft: '12px', flex: 1 }}>
                      <div style={{ fontWeight: '500' }}>Saffron Milk</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>For: Sleep, Relaxation</div>
                    </div>
                    <button style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: 'rgba(255, 111, 97, 0.1)',
                      color: '#FF6F61',
                      borderRadius: '50%',
                      border: 'none',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <i className="fa-solid fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <div style={{
        position: 'fixed',
        right: '24px',
        bottom: '24px',
        zIndex: 30
      }}>
        <button style={{
          width: '56px',
          height: '56px',
          backgroundColor: '#FF6F61',
          color: 'white',
          borderRadius: '50%',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          border: 'none',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'background-color 0.3s ease'
        }} onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
           onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
           onClick={() => navigate('/quiz')}>
          <i className="fa-solid fa-brain" style={{ fontSize: '20px' }}></i>
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {showSidebar && (
        <div style={{
          position: 'fixed',
          inset: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 30
        }} onClick={toggleSidebar}></div>
      )}
    </div>
  );
}

export default AdminDashboard;
