<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>::-webkit-scrollbar { display: none;}</style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "coral": "#FF6F61",
        "peach": "#FFDAB9",
        "teal": {
          "DEFAULT": "#006D77",
          "light": "#83C5BE"
        },
        "cream": "#F5E8C7"
      },
      "fontFamily": {
        "inter": [
          "Inter",
          "sans-serif"
        ],
        "sans": [
          "Inter",
          "sans-serif"
        ]
      },
      "borderRadius": {
        "card": "12px",
        "button": "8px"
      },
      "boxShadow": {
        "card": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        "card-hover": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        "appbar": "0 2px 4px rgba(0, 0, 0, 0.1)"
      }
    }
  }
};</script>
    <script src="https://code.highcharts.com/highcharts.js"></script>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style>
</head>
<body class="bg-cream font-inter">
    <!-- App Bar -->
    <div id="header" class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-coral to-peach shadow-appbar h-16 flex items-center px-4 md:px-6">
        <div class="flex items-center">
            <button id="menu-toggle" class="mr-4 text-white md:hidden">
                <i class="fa-solid fa-bars text-xl"></i>
            </button>
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-2">
                    <i class="fa-solid fa-utensils text-coral"></i>
                </div>
                <span class="font-bold text-white text-xl">LiberateBites</span>
            </div>
        </div>
        
        <div class="hidden md:flex ml-8 relative flex-grow max-w-md">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i class="fa-solid fa-search text-gray-400"></i>
            </div>
            <input type="text" class="pl-10 pr-4 py-2 w-full rounded-button bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-teal" placeholder="Search foods, books, moods...">
        </div>
        
        <div class="ml-auto flex items-center space-x-2 md:space-x-4">
            <div class="hidden md:flex items-center bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <span class="text-white text-sm">Welcome back, <span class="font-medium">Aisha</span></span>
            </div>
            
            <div class="relative">
                <button class="relative p-2 text-white">
                    <i class="fa-solid fa-bell"></i>
                    <span class="absolute -top-1 -right-1 w-5 h-5 bg-teal text-white text-xs rounded-full flex items-center justify-center">3</span>
                </button>
            </div>
            
            <div class="w-8 h-8 rounded-full bg-cover bg-center" style="background-image: url('https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-5.jpg')"></div>
        </div>
    </div>

    <!-- Left Sidebar Navigation -->
    <div id="sidebar" class="fixed top-0 left-0 bottom-0 w-64 bg-white shadow-lg transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out z-40 pt-16 flex flex-col">
        <div class="flex-1 overflow-y-auto p-4">
            <nav class="space-y-1">
                <span class="flex items-center px-4 py-3 bg-coral/10 text-coral rounded-button cursor-pointer">
                    <i class="fa-solid fa-home w-5 text-center"></i>
                    <span class="ml-3 font-medium">Dashboard</span>
                </span>
                <span class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-button group cursor-pointer">
                    <i class="fa-solid fa-brain w-5 text-center"></i>
                    <span class="ml-3">Mood Quiz</span>
                    <span class="ml-auto px-2 py-0.5 text-xs bg-teal text-white rounded-full">New</span>
                </span>
                <span class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-button cursor-pointer">
                    <i class="fa-solid fa-utensils w-5 text-center"></i>
                    <span class="ml-3">Menu Explorer</span>
                </span>
                <span class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-button cursor-pointer">
                    <i class="fa-solid fa-book w-5 text-center"></i>
                    <span class="ml-3">Book Recommendations</span>
                </span>
                <span class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-button cursor-pointer">
                    <i class="fa-solid fa-chart-line w-5 text-center"></i>
                    <span class="ml-3">My Journey</span>
                </span>
                <span class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-button cursor-pointer">
                    <i class="fa-solid fa-users w-5 text-center"></i>
                    <span class="ml-3">Community</span>
                </span>
                <span class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-button cursor-pointer">
                    <i class="fa-solid fa-gear w-5 text-center"></i>
                    <span class="ml-3">Settings</span>
                </span>
            </nav>
        </div>
        
        <div class="p-4 border-t border-gray-200">
            <div class="bg-gradient-to-r from-teal to-coral/60 rounded-card p-4 text-white mb-4">
                <div class="flex items-center mb-2">
                    <i class="fa-solid fa-fire-flame-curved text-xl"></i>
                    <span class="ml-2 font-semibold">Mood Streak</span>
                </div>
                <div class="text-2xl font-bold mb-1">12 Days</div>
                <div class="text-xs opacity-80">Keep tracking your moods to grow your streak!</div>
            </div>
            
            <div class="text-xs text-gray-500 text-center">
                <div>Need help? <span class="text-teal cursor-pointer">Contact support</span></div>
                <div class="mt-1">LiberateBites v2.1.0</div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div id="main-content" class="pt-16 md:pl-64 min-h-screen">
        <div class="max-w-7xl mx-auto p-4 md:p-6">
            <!-- Welcome Section -->
            <div id="welcome-section" class="grid grid-cols-1 md:grid-cols-12 gap-6 mb-6">
                <div class="md:col-span-8">
                    <div class="bg-white rounded-card shadow-card p-6">
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-800">Good afternoon, Aisha!</h1>
                        <p class="text-gray-600 mt-1">Ready to discover your perfect meal match?</p>
                        
                        <div class="mt-4">
                            <div class="flex justify-between mb-1">
                                <span class="text-sm text-gray-600">Weekly mood tracking</span>
                                <span class="text-sm font-medium text-teal">5/7 days</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-teal h-2.5 rounded-full" style="width: 71%"></div>
                            </div>
                        </div>
                        
                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-teal/10 text-teal rounded-full text-sm">Calm</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">Focused</span>
                            <span class="px-3 py-1 bg-amber-100 text-amber-700 rounded-full text-sm">Comfort-seeking</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">Reflective</span>
                        </div>
                    </div>
                </div>
                
                <div class="md:col-span-4">
                    <div class="bg-white rounded-card shadow-card p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
                        <div class="space-y-3">
                            <button class="w-full py-2.5 px-4 bg-coral text-white rounded-button hover:bg-coral/90 transition flex items-center justify-center">
                                <i class="fa-solid fa-bolt mr-2"></i>
                                <span>Feeling Lucky</span>
                            </button>
                            <button class="w-full py-2.5 px-4 border border-teal text-teal rounded-button hover:bg-teal/5 transition flex items-center justify-center">
                                <i class="fa-solid fa-rotate-left mr-2"></i>
                                <span>Repeat Last Order</span>
                            </button>
                            <button class="w-full py-2.5 px-4 text-gray-700 hover:bg-gray-100 rounded-button transition flex items-center justify-center">
                                <i class="fa-solid fa-history mr-2"></i>
                                <span>View History</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Core Action Cards Section -->
            <div id="core-actions" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div id="mood-quiz-card" class="bg-white rounded-card shadow-card hover:shadow-card-hover transition-shadow duration-300">
                    <div class="h-48 rounded-t-card bg-cover bg-center relative" style="background-image: url('https://images.unsplash.com/photo-1499209974431-9dddcece7f88?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80')">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-t-card"></div>
                        <div class="absolute bottom-4 left-6 text-white">
                            <h3 class="text-xl font-bold">Discover Through Your Mood</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">Take our 5-minute personalized assessment to find the perfect food and book matches for your current emotional state.</p>
                        
                        <ul class="space-y-2 mb-4">
                            <li class="flex items-start">
                                <i class="fa-solid fa-sparkles text-coral mt-1 w-5"></i>
                                <span class="ml-2">100+ emotional states analyzed</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fa-solid fa-book text-coral mt-1 w-5"></i>
                                <span class="ml-2">Matching book recommendations</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fa-solid fa-bullseye text-coral mt-1 w-5"></i>
                                <span class="ml-2">Personalized results just for you</span>
                            </li>
                        </ul>
                        
                        <div class="flex items-center justify-between mt-4">
                            <button class="px-6 py-2.5 bg-coral text-white rounded-button hover:bg-coral/90 transition">
                                Start Mood Quiz
                            </button>
                            <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">Last result: Comfort Food</span>
                        </div>
                    </div>
                </div>
                
                <div id="menu-explorer-card" class="bg-white rounded-card shadow-card hover:shadow-card-hover transition-shadow duration-300">
                    <div class="h-48 rounded-t-card bg-cover bg-center relative" style="background-image: url('https://images.unsplash.com/photo-1567337710282-00832b415979?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80')">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-t-card"></div>
                        <div class="absolute bottom-4 left-6 text-white">
                            <h3 class="text-xl font-bold">Browse Our Menu</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">Explore curated vegetarian comfort foods that nourish both body and mind, each tagged with emotional benefits.</p>
                        
                        <ul class="space-y-2 mb-4">
                            <li class="flex items-start">
                                <i class="fa-solid fa-seedling text-teal mt-1 w-5"></i>
                                <span class="ml-2">100% vegetarian cuisine</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fa-solid fa-heart text-teal mt-1 w-5"></i>
                                <span class="ml-2">Comfort food specialists</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fa-solid fa-tag text-teal mt-1 w-5"></i>
                                <span class="ml-2">Mood-tagged recipes</span>
                            </li>
                        </ul>
                        
                        <div class="flex items-center justify-between mt-4">
                            <button class="px-6 py-2.5 border border-teal text-teal rounded-button hover:bg-teal/5 transition">
                                Explore Menu
                            </button>
                            <span class="px-3 py-1 bg-amber-100 text-amber-700 rounded-full text-sm">Popular: Rajma-Chawal Bowl</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Personal Insights Section -->
            <div id="personal-insights" class="mb-8">
                <div class="bg-gradient-to-r from-teal to-coral rounded-card p-6">
                    <h2 class="text-xl font-bold text-white mb-4">Your Wellness Journey</h2>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-white/90 backdrop-blur-sm rounded-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 rounded-full bg-teal/20 flex items-center justify-center">
                                    <i class="fa-solid fa-clipboard-check text-teal"></i>
                                </div>
                                <span class="text-3xl font-bold text-gray-800">12</span>
                            </div>
                            <div class="text-sm text-gray-600">Quizzes Completed</div>
                        </div>
                        
                        <div class="bg-white/90 backdrop-blur-sm rounded-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 rounded-full bg-coral/20 flex items-center justify-center">
                                    <i class="fa-solid fa-face-smile text-coral"></i>
                                </div>
                                <span class="text-3xl font-bold text-gray-800">8</span>
                            </div>
                            <div class="text-sm text-gray-600">Moods Tracked</div>
                        </div>
                        
                        <div class="bg-white/90 backdrop-blur-sm rounded-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                                    <i class="fa-solid fa-utensils text-amber-600"></i>
                                </div>
                                <span class="text-3xl font-bold text-gray-800">24</span>
                            </div>
                            <div class="text-sm text-gray-600">Meals Enjoyed</div>
                        </div>
                        
                        <div class="bg-white/90 backdrop-blur-sm rounded-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                                    <i class="fa-solid fa-book-open text-purple-600"></i>
                                </div>
                                <span class="text-3xl font-bold text-gray-800">5</span>
                            </div>
                            <div class="text-sm text-gray-600">Books Discovered</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity & Trending Section -->
            <div id="activity-section" class="grid grid-cols-1 md:grid-cols-12 gap-6 mb-8">
                <div class="md:col-span-8">
                    <div class="bg-white rounded-card shadow-card p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fa-solid fa-clock-rotate-left mr-2 text-teal"></i>
                            Recent Activity
                        </h2>
                        
                        <div class="space-y-6">
                            <div class="flex">
                                <div class="flex-shrink-0 w-12 text-center">
                                    <div class="w-8 h-8 bg-coral/20 rounded-full flex items-center justify-center mx-auto">
                                        <i class="fa-solid fa-utensils text-coral"></i>
                                    </div>
                                    <div class="w-0.5 h-full bg-gray-200 mx-auto mt-2"></div>
                                </div>
                                <div class="flex-1 ml-4">
                                    <div class="text-sm text-gray-500">Yesterday</div>
                                    <div class="font-medium">Feeling Stressed → Rajma-Chawal Bowl</div>
                                    <div class="text-sm text-gray-600 mt-1">Paired with 'Ikigai' summary</div>
                                    <div class="mt-2">
                                        <span class="px-2 py-0.5 bg-red-100 text-red-700 rounded-full text-xs">Stress Relief</span>
                                        <span class="px-2 py-0.5 bg-amber-100 text-amber-700 rounded-full text-xs ml-1">Comfort Food</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0 w-12 text-center">
                                    <div class="w-8 h-8 bg-teal/20 rounded-full flex items-center justify-center mx-auto">
                                        <i class="fa-solid fa-brain text-teal"></i>
                                    </div>
                                    <div class="w-0.5 h-full bg-gray-200 mx-auto mt-2"></div>
                                </div>
                                <div class="flex-1 ml-4">
                                    <div class="text-sm text-gray-500">2 days ago</div>
                                    <div class="font-medium">Overthinking → Tomato Soup</div>
                                    <div class="text-sm text-gray-600 mt-1">Paired with 'The Power of Now'</div>
                                    <div class="mt-2">
                                        <span class="px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">Calming</span>
                                        <span class="px-2 py-0.5 bg-purple-100 text-purple-700 rounded-full text-xs ml-1">Mindfulness</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0 w-12 text-center">
                                    <div class="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mx-auto">
                                        <i class="fa-solid fa-mug-hot text-amber-600"></i>
                                    </div>
                                </div>
                                <div class="flex-1 ml-4">
                                    <div class="text-sm text-gray-500">Last week</div>
                                    <div class="font-medium">Lonely → Masala Milk Tea</div>
                                    <div class="text-sm text-gray-600 mt-1">Paired with 'Tuesdays with Morrie'</div>
                                    <div class="mt-2">
                                        <span class="px-2 py-0.5 bg-green-100 text-green-700 rounded-full text-xs">Comforting</span>
                                        <span class="px-2 py-0.5 bg-indigo-100 text-indigo-700 rounded-full text-xs ml-1">Connection</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button class="mt-4 text-teal flex items-center text-sm">
                            <span>View all activity</span>
                            <i class="fa-solid fa-chevron-right ml-1 text-xs"></i>
                        </button>
                    </div>
                </div>
                
                <div class="md:col-span-4">
                    <div class="bg-teal/10 rounded-card shadow-card p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fa-solid fa-fire mr-2 text-coral"></i>
                            Trending Today
                        </h2>
                        
                        <div class="space-y-4">
                            <div class="flex items-center bg-white rounded-lg p-3">
                                <div class="w-12 h-12 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1631292784640-2b24be148025?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=200&amp;q=80')"></div>
                                <div class="ml-3 flex-1">
                                    <div class="font-medium">Chana Masala Bowl</div>
                                    <div class="text-xs text-gray-500">For: Motivation, Energy</div>
                                </div>
                                <button class="w-8 h-8 bg-coral/10 text-coral rounded-full flex items-center justify-center">
                                    <i class="fa-solid fa-plus"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center bg-white rounded-lg p-3">
                                <div class="w-12 h-12 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1600271886742-f049cd451bba?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=200&amp;q=80')"></div>
                                <div class="ml-3 flex-1">
                                    <div class="font-medium">Coconut Kheer</div>
                                    <div class="text-xs text-gray-500">For: Comfort, Nostalgia</div>
                                </div>
                                <button class="w-8 h-8 bg-coral/10 text-coral rounded-full flex items-center justify-center">
                                    <i class="fa-solid fa-plus"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center bg-white rounded-lg p-3">
                                <div class="w-12 h-12 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1563379926898-05f4575a45d8?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=200&amp;q=80')"></div>
                                <div class="ml-3 flex-1">
                                    <div class="font-medium">Lemon Ginger Tea</div>
                                    <div class="text-xs text-gray-500">For: Focus, Clarity</div>
                                </div>
                                <button class="w-8 h-8 bg-coral/10 text-coral rounded-full flex items-center justify-center">
                                    <i class="fa-solid fa-plus"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center bg-white rounded-lg p-3">
                                <div class="w-12 h-12 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1547592180-85f173990554?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=200&amp;q=80')"></div>
                                <div class="ml-3 flex-1">
                                    <div class="font-medium">Saffron Milk</div>
                                    <div class="text-xs text-gray-500">For: Sleep, Relaxation</div>
                                </div>
                                <button class="w-8 h-8 bg-coral/10 text-coral rounded-full flex items-center justify-center">
                                    <i class="fa-solid fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mood Insights Chart -->
            <div id="mood-chart" class="mb-8">
                <div class="bg-white rounded-card shadow-card p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fa-solid fa-chart-line mr-2 text-teal"></i>
                        Your Mood Patterns
                    </h2>
                    
                    <div class="h-[300px]" id="mood-chart-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Floating Action Button -->
    <div class="fixed right-6 bottom-6 z-30">
        <button class="w-14 h-14 bg-coral text-white rounded-full shadow-lg flex items-center justify-center hover:bg-coral/90 transition-colors">
            <i class="fa-solid fa-brain text-xl"></i>
        </button>
    </div>
    
    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black/50 z-30 hidden"></div>
    
    <script>
        // Menu Toggle
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');
            
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });
        
        document.getElementById('mobile-menu-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');
            
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });
        
        // Mood Chart
        document.addEventListener('DOMContentLoaded', function() {
            Highcharts.chart('mood-chart-container', {
                chart: {
                    type: 'line',
                    style: {
                        fontFamily: 'Inter, sans-serif'
                    }
                },
                title: {
                    text: ''
                },
                xAxis: {
                    categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                    title: {
                        text: 'Mood Score'
                    }
                },
                plotOptions: {
                    line: {
                        marker: {
                            symbol: 'circle'
                        }
                    }
                },
                colors: ['#FF6F61', '#006D77', '#83C5BE'],
                series: [{
                    name: 'Energy',
                    data: [3, 5, 6, 4, 7, 8, 6]
                }, {
                    name: 'Happiness',
                    data: [7, 6, 5, 6, 5, 8, 9]
                }, {
                    name: 'Calmness',
                    data: [5, 4, 6, 7, 8, 6, 7]
                }],
                legend: {
                    align: 'center',
                    verticalAlign: 'bottom'
                },
                credits: {
                    enabled: false
                }
            });
        });
    </script>
</body>
</html>