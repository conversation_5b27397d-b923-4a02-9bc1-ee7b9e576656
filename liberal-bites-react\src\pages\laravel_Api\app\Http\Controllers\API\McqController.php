<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\McqQuestion;
use App\Models\McqOption;
use Illuminate\Http\Request;

class McqController extends Controller
{
    public function index()
    {
        return McqQuestion::with('options')->get();
    }

    public function store(Request $request)
    {
        $request->validate([
            'question' => 'required|string',
            'options'  => 'required|array|size:4',
            'options.*.option_text' => 'required|string',
            'options.*.points'      => 'required|integer',
        ]);

        $question = McqQuestion::create(['question' => $request->question]);

        foreach ($request->options as $opt) {
            McqOption::create([
                'question_id' => $question->id,
                'option_text' => $opt['option_text'],
                'points'      => $opt['points'],
            ]);
        }

        return response()->json(['message' => 'MCQ created successfully'], 201);
    }

    public function show($id)
    {
        return McqQuestion::with('options')->findOrFail($id);
    }

    public function update(Request $request, $id)
    {
        $question = McqQuestion::findOrFail($id);
        $question->update(['question' => $request->question]);

        if ($request->has('options')) {
            foreach ($request->options as $opt) {
                $option = McqOption::find($opt['id']);
                if ($option) {
                    $option->update([
                        'option_text' => $opt['option_text'],
                        'points'      => $opt['points']
                    ]);
                }
            }
        }

        return response()->json(['message' => 'MCQ updated successfully']);
    }

    public function destroy($id)
    {
        $question = McqQuestion::findOrFail($id);
        $question->delete();

        return response()->json(['message' => 'MCQ deleted successfully']);
    }

    public function destroyOption($id)
    {
        $option = McqOption::findOrFail($id);
        $option->delete();

        return response()->json(['message' => 'Option deleted successfully']);
    }
}

