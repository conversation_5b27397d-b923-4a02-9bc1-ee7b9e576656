# Liberal Bites - Mood Data Auto-Update System

## Overview
This system automatically updates the mood categories and sub-moods data when the Laravel application starts up. It replaces the demo data with the real Liberal Bites mood specification.

## Features
- **Auto-execution**: Runs automatically when the application loads
- **Safety checks**: Prevents duplicate updates and data corruption
- **Logging**: All operations are logged for monitoring
- **Easy disable**: Simple boolean flag to turn off auto-update
- **Manual trigger**: Web routes for testing and verification

## How It Works

### 1. Auto-Update on App Start
The update system is implemented in `app/Providers/AppServiceProvider.php` in the `boot()` method. Every time the Laravel application starts, it:

1. Checks if mood data needs updating
2. Compares current data with expected structure
3. Only updates if necessary (avoids duplicate operations)
4. Logs all operations

### 2. Data Structure
- **20 Main Mood Categories**: From "Overwhelmed Mind" to "See<PERSON>'s Soul"
- **100 Sub-Moods**: 5 sub-moods per category, each with name and meaning
- **Preserves relationships**: Maintains all foreign key constraints

### 3. Safety Features
- **Truncate and rebuild**: Safely removes old data and inserts new
- **Transaction safety**: Uses <PERSON>'s built-in database safety
- **Duplicate prevention**: Checks existing data before updating
- **Error handling**: Catches and logs any errors

## Usage Instructions

### Enable/Disable Auto-Update
In `app/Providers/AppServiceProvider.php`, line 23:
```php
// Set to true to enable, false to disable
$enableMoodDataUpdate = true;  // Change to false to disable
```

### Manual Testing Routes
After uploading files to server, you can test using these URLs:

1. **Check current data status**:
   ```
   https://yourserver.com/mood-data-status
   ```

2. **Manually trigger update**:
   ```
   https://yourserver.com/update-mood-data
   ```

### Deployment Workflow
1. **Local Development**: Make changes with auto-update enabled
2. **Upload to Server**: Files automatically trigger update on first load
3. **Verify**: Check `/mood-data-status` to confirm update worked
4. **Disable**: Set `$enableMoodDataUpdate = false` and re-upload
5. **Production**: Auto-update is now disabled for normal operation

## Monitoring

### Log Files
Check Laravel logs for update status:
- Success: "Liberal Bites: Mood data update completed successfully!"
- Skip: "Liberal Bites: Mood data is already up to date."
- Error: "Liberal Bites: Error updating mood data: [error message]"

### Expected Data Counts
- **Moods**: 20 categories
- **Sub-Moods**: 100 total (5 per category)

## Data Preservation
The system:
- ✅ **Preserves**: All table structures, columns, and relationships
- ✅ **Preserves**: Non-mood related data (users, books, food items, etc.)
- ✅ **Updates**: Only mood categories and sub-moods content
- ✅ **Maintains**: All foreign key relationships

## Troubleshooting

### If Update Doesn't Run
1. Check if `$enableMoodDataUpdate` is set to `true`
2. Check Laravel logs for error messages
3. Verify database permissions
4. Try manual update route: `/update-mood-data`

### If Data Looks Wrong
1. Check `/mood-data-status` for current state
2. Review logs for any error messages
3. Manually trigger update via `/update-mood-data`

### To Completely Reset
1. Set `$enableMoodDataUpdate = true`
2. Visit `/update-mood-data` to force refresh
3. Check `/mood-data-status` to verify
4. Set `$enableMoodDataUpdate = false` when satisfied

## Files Modified
- `app/Providers/AppServiceProvider.php` - Main auto-update logic
- `routes/web.php` - Testing and status routes
- `MOOD_DATA_UPDATE_README.md` - This documentation

## Security Notes
- Testing routes should be removed or protected in production
- Auto-update should be disabled after successful deployment
- Monitor logs for any unauthorized update attempts
