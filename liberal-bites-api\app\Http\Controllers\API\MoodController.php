<?php

namespace App\Http\Controllers\API;

use App\Models\Mood;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class MoodController extends Controller
{
    public function index()
    {
        return response()->json(Mood::with('subMoods')->get());
    }

    public function store(Request $request)
    {
        $request->validate(['name' => 'required|string']);
        $mood = Mood::create($request->all());
        return response()->json($mood, 201);
    }

    public function show($id)
    {
        return response()->json(Mood::with('subMoods')->findOrFail($id));
    }

    public function update(Request $request, $id)
    {
        $mood = Mood::findOrFail($id);
        $mood->update($request->all());
        return response()->json($mood);
    }

    public function destroy($id)
    {
        Mood::findOrFail($id)->delete();
        return response()->json(['message' => 'Mood deleted']);
    }
}

