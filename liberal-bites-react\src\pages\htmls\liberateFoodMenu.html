<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>::-webkit-scrollbar { display: none;}</style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "coral": "#FF6F61",
        "teal": "#006D77",
        "peach": "#FFDAB9",
        "beige": "#F5E8C7",
        "yellow": "#FFF3B0"
      },
      "fontFamily": {
        "inter": [
          "Inter",
          "sans-serif"
        ],
        "poppins": [
          "Poppins",
          "sans-serif"
        ],
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
    <style>
        .masonry-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            grid-gap: 24px;
        }
        
        @media (min-width: 768px) {
            .masonry-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (min-width: 1024px) {
            .masonry-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .food-card {
            transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        }
        
        .food-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .food-info {
            transition: opacity 0.3s ease-in-out;
        }

        .mood-tab {
            transition: all 0.3s ease-in-out;
        }

        .mood-tab:hover:not(.active) {
            background-color: rgba(255, 111, 97, 0.1);
        }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head>
<body class="bg-beige font-inter">
    <!-- Header -->
    <header id="header" class="sticky top-0 bg-white shadow-md z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <h1 class="text-2xl font-poppins font-bold text-coral">LiberateBites</h1>
            </div>
            <nav class="hidden md:flex items-center space-x-6">
                <span class="text-gray-700 hover:text-coral transition-colors duration-300 cursor-pointer">Home</span>
                <span class="text-gray-700 hover:text-coral transition-colors duration-300 cursor-pointer">Menu</span>
                <span class="text-gray-700 hover:text-coral transition-colors duration-300 cursor-pointer">About</span>
                <span class="text-gray-700 hover:text-coral transition-colors duration-300 cursor-pointer">Contact</span>
            </nav>
            <div class="flex items-center space-x-4">
                <button class="text-gray-700 hover:text-coral transition-colors duration-300">
                    <i class="fa-regular fa-heart text-lg"></i>
                </button>
                <button class="text-gray-700 hover:text-coral transition-colors duration-300 relative">
                    <i class="fa-solid fa-shopping-bag text-lg"></i>
                    <span class="absolute -top-2 -right-2 bg-coral text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                </button>
                <button class="md:hidden text-gray-700">
                    <i class="fa-solid fa-bars text-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero-section" class="relative h-[500px] flex items-center justify-center overflow-hidden">
        <div class="absolute inset-0 bg-black/40 z-10"></div>
        <img class="absolute inset-0 w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/bac237752c-10da0d2f69d679c96006.png" alt="beautiful vegetarian food spread on a table, artistic food photography, moody lighting, high quality">
        
        <div class="container mx-auto px-4 relative z-20 text-center">
            <h1 class="text-4xl md:text-5xl font-poppins font-bold text-white mb-6">Curated for Your Mood</h1>
            <p class="text-lg text-white/90 mb-8 max-w-2xl mx-auto">Discover vegetarian dishes that nourish both body and mind, perfectly matched to how you feel today.</p>
            
            <!-- Search Bar -->
            <div id="search-container" class="relative max-w-2xl mx-auto mb-8">
                <input type="text" placeholder="Search for dishes, ingredients, or moods..." class="w-full py-4 px-6 rounded-full bg-white/95 text-gray-800 focus:outline-none focus:ring-2 focus:ring-coral shadow-lg">
                <button class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-coral text-white p-3 rounded-full hover:bg-teal transition-colors duration-300">
                    <i class="fa-solid fa-search"></i>
                </button>
            </div>
            
            <!-- Mood Tabs -->
            <div id="mood-tabs" class="flex flex-wrap justify-center gap-2 md:gap-4">
                <button class="mood-tab active bg-coral text-white py-2 px-6 rounded-full font-medium text-sm md:text-base shadow-md">
                    <i class="fa-solid fa-house-heart mr-2"></i>Comfort
                </button>
                <button class="mood-tab bg-white/90 text-gray-700 py-2 px-6 rounded-full font-medium text-sm md:text-base shadow-md">
                    <i class="fa-solid fa-bolt mr-2"></i>Energy
                </button>
                <button class="mood-tab bg-white/90 text-gray-700 py-2 px-6 rounded-full font-medium text-sm md:text-base shadow-md">
                    <i class="fa-solid fa-peace mr-2"></i>Calm
                </button>
                <button class="mood-tab bg-white/90 text-gray-700 py-2 px-6 rounded-full font-medium text-sm md:text-base shadow-md">
                    <i class="fa-solid fa-brain mr-2"></i>Focus
                </button>
                <button class="mood-tab bg-white/90 text-gray-700 py-2 px-6 rounded-full font-medium text-sm md:text-base shadow-md">
                    <i class="fa-solid fa-face-smile mr-2"></i>Joy
                </button>
            </div>
        </div>
    </section>

    <!-- Recommended Section -->
    <section id="recommended-section" class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl md:text-3xl font-poppins font-bold text-gray-800">Recommended for You</h2>
                <span class="text-coral hover:text-teal transition-colors duration-300 flex items-center cursor-pointer">
                    <span>View all</span>
                    <i class="fa-solid fa-arrow-right ml-2"></i>
                </span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Recommended Card 1 -->
                <div id="rec-card-1" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-56">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/96c3a4e649-a2c0c47540d85c868ae3.png" alt="colorful buddha bowl with roasted vegetables, avocado, and quinoa, vegetarian food, overhead shot">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-yellow/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-sparkles mr-1"></i>Joy
                            </span>
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-bolt mr-1"></i>Energy
                            </span>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Rainbow Buddha Bowl</h3>
                            <span class="text-coral font-bold">$14.95</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">A vibrant mix of roasted vegetables, fresh greens, and protein-packed quinoa with our signature tahini dressing.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegan</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Gluten-Free</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">High-Protein</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Recommended Card 2 -->
                <div id="rec-card-2" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-56">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/0c055abc93-4e1970e151def2694c60.png" alt="cozy butternut squash soup with herbs and croutons, vegetarian comfort food, warm lighting">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-peach/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-house-heart mr-1"></i>Comfort
                            </span>
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-peace mr-1"></i>Calm
                            </span>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Butternut Bliss Soup</h3>
                            <span class="text-coral font-bold">$11.50</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Velvety butternut squash soup with aromatic herbs and crunchy sourdough croutons. Pure comfort in a bowl.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegetarian</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Gluten-Free Option</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Recommended Card 3 -->
                <div id="rec-card-3" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-56">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/301d1175a5-7d75aa4c46093172ac33.png" alt="matcha green tea smoothie bowl with berries and granola, vegetarian breakfast, bright natural lighting">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-brain mr-1"></i>Focus
                            </span>
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-bolt mr-1"></i>Energy
                            </span>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Matcha Energy Bowl</h3>
                            <span class="text-coral font-bold">$13.75</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Antioxidant-rich matcha blended with banana and topped with seasonal berries, hemp seeds, and house-made granola.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegan</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Antioxidant-Rich</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Menu Filter Section -->
    <section id="menu-filter" class="py-8 bg-beige/50">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <h2 class="text-2xl md:text-3xl font-poppins font-bold text-gray-800">Explore Our Menu</h2>
                
                <div class="flex flex-wrap gap-3">
                    <div class="relative">
                        <select class="appearance-none bg-white border border-gray-200 text-gray-700 py-2 px-4 pr-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-coral">
                            <option>All Categories</option>
                            <option>Bowls &amp; Salads</option>
                            <option>Soups &amp; Stews</option>
                            <option>Breakfast</option>
                            <option>Mains</option>
                            <option>Desserts</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <i class="fa-solid fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="appearance-none bg-white border border-gray-200 text-gray-700 py-2 px-4 pr-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-coral">
                            <option>Sort by: Recommended</option>
                            <option>Price: Low to High</option>
                            <option>Price: High to Low</option>
                            <option>Most Popular</option>
                            <option>Mood Match</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <i class="fa-solid fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <button class="bg-white border border-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                        <i class="fa-solid fa-sliders mr-2"></i>Filters
                    </button>
                </div>
            </div>
            
            <!-- Dietary Filters -->
            <div class="flex flex-wrap gap-3 mb-8">
                <button class="bg-coral/10 border border-coral/20 text-coral py-1 px-4 rounded-full hover:bg-coral/20 transition-colors duration-300 text-sm font-medium">
                    All
                </button>
                <button class="bg-white border border-gray-200 text-gray-700 py-1 px-4 rounded-full hover:bg-gray-50 transition-colors duration-300 text-sm font-medium">
                    <i class="fa-solid fa-leaf mr-1 text-green-600"></i>Vegan
                </button>
                <button class="bg-white border border-gray-200 text-gray-700 py-1 px-4 rounded-full hover:bg-gray-50 transition-colors duration-300 text-sm font-medium">
                    <i class="fa-solid fa-wheat-awn-circle-exclamation mr-1 text-amber-600"></i>Gluten-Free
                </button>
                <button class="bg-white border border-gray-200 text-gray-700 py-1 px-4 rounded-full hover:bg-gray-50 transition-colors duration-300 text-sm font-medium">
                    <i class="fa-solid fa-seedling mr-1 text-green-500"></i>Organic
                </button>
                <button class="bg-white border border-gray-200 text-gray-700 py-1 px-4 rounded-full hover:bg-gray-50 transition-colors duration-300 text-sm font-medium">
                    <i class="fa-solid fa-fire-flame-simple mr-1 text-orange-500"></i>Spicy
                </button>
                <button class="bg-white border border-gray-200 text-gray-700 py-1 px-4 rounded-full hover:bg-gray-50 transition-colors duration-300 text-sm font-medium">
                    <i class="fa-solid fa-dumbbell mr-1 text-gray-700"></i>Protein-Rich
                </button>
            </div>
        </div>
    </section>

    <!-- Menu Grid Section -->
    <section id="menu-grid" class="py-12 bg-beige/50">
        <div class="container mx-auto px-4">
            <div class="masonry-grid">
                <!-- Menu Card 1 -->
                <div id="menu-card-1" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-64">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/b6b990e147-015d3686bfd37647d588.png" alt="vegetarian mushroom risotto with herbs, elegant plating, restaurant quality">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-peach/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-house-heart mr-1"></i>Comfort
                            </span>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 hover:opacity-100 transition-opacity duration-300 food-info">
                            <h4 class="font-medium mb-1">Ingredients:</h4>
                            <p class="text-xs">Arborio rice, wild mushrooms, white wine, vegetable broth, parmesan, herbs</p>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Wild Mushroom Risotto</h3>
                            <span class="text-coral font-bold">$16.95</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Creamy arborio rice with wild mushrooms, finished with aged parmesan and fresh herbs.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegetarian</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Award-Winning</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Menu Card 2 -->
                <div id="menu-card-2" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-72">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/88918df2bd-3460051e304ef1f50f64.png" alt="vegetarian thai green curry with tofu and vegetables, steaming hot, vibrant colors">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-yellow/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-sparkles mr-1"></i>Joy
                            </span>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 hover:opacity-100 transition-opacity duration-300 food-info">
                            <h4 class="font-medium mb-1">Ingredients:</h4>
                            <p class="text-xs">Coconut milk, green curry paste, tofu, seasonal vegetables, thai basil, lime</p>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Green Thai Curry</h3>
                            <span class="text-coral font-bold">$15.50</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Aromatic Thai green curry with crispy tofu, seasonal vegetables, and fragrant jasmine rice.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegan</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Gluten-Free</span>
                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Spicy</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-solid fa-heart text-coral"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Menu Card 3 -->
                <div id="menu-card-3" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-60">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/f36500d799-c522bf3fb07474157133.png" alt="vegetarian avocado toast with poached egg, microgreens, and edible flowers, bright natural lighting">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-bolt mr-1"></i>Energy
                            </span>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 hover:opacity-100 transition-opacity duration-300 food-info">
                            <h4 class="font-medium mb-1">Ingredients:</h4>
                            <p class="text-xs">Sourdough bread, avocado, poached egg, microgreens, chili flakes, sea salt</p>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Artisanal Avocado Toast</h3>
                            <span class="text-coral font-bold">$12.75</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Crusty sourdough topped with smashed avocado, perfectly poached egg, and delicate microgreens.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegetarian</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">High-Protein</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Menu Card 4 -->
                <div id="menu-card-4" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-64">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/823a6498a7-6a87945d1e52a0c0d81b.png" alt="vegetarian lentil shepherd">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-peach/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-house-heart mr-1"></i>Comfort
                            </span>
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-peace mr-1"></i>Calm
                            </span>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 hover:opacity-100 transition-opacity duration-300 food-info">
                            <h4 class="font-medium mb-1">Ingredients:</h4>
                            <p class="text-xs">French lentils, root vegetables, herbs, sweet potato, organic butter, nutmeg</p>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Lentil Shepherd's Pie</h3>
                            <span class="text-coral font-bold">$15.95</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Hearty French lentils and seasonal vegetables topped with creamy sweet potato mash.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegetarian</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Gluten-Free</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Menu Card 5 -->
                <div id="menu-card-5" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-72">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/2b981daa92-5b2d04e4372706381f59.png" alt="vegetarian berry chia pudding parfait with layers of yogurt and granola, breakfast food, bright colors">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-brain mr-1"></i>Focus
                            </span>
                            <span class="bg-yellow/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-sparkles mr-1"></i>Joy
                            </span>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 hover:opacity-100 transition-opacity duration-300 food-info">
                            <h4 class="font-medium mb-1">Ingredients:</h4>
                            <p class="text-xs">Chia seeds, coconut milk, maple syrup, mixed berries, Greek yogurt, house-made granola</p>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Berry Bliss Chia Parfait</h3>
                            <span class="text-coral font-bold">$10.50</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Layered chia seed pudding with coconut milk, seasonal berries, and crunchy house-made granola.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegetarian</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Gluten-Free</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Omega-Rich</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Menu Card 6 -->
                <div id="menu-card-6" class="food-card bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="relative h-64">
                        <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/d4d85f5af7-f009a84dd72ef239b47c.png" alt="vegetarian stuffed bell peppers with quinoa and black beans, colorful presentation, overhead shot">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <span class="bg-white/90 text-teal text-xs px-2 py-1 rounded-full font-medium">
                                <i class="fa-solid fa-bolt mr-1"></i>Energy
                            </span>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 hover:opacity-100 transition-opacity duration-300 food-info">
                            <h4 class="font-medium mb-1">Ingredients:</h4>
                            <p class="text-xs">Bell peppers, quinoa, black beans, corn, tomatoes, spices, avocado crema</p>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">Quinoa Stuffed Peppers</h3>
                            <span class="text-coral font-bold">$14.25</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Vibrant bell peppers stuffed with protein-rich quinoa, black beans, and Southwest spices.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Vegan</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Gluten-Free</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">High-Protein</span>
                        </div>
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-coral hover:bg-teal text-white py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium">
                                Add to Cart
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors duration-300">
                                <i class="fa-solid fa-heart text-coral"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button class="bg-teal hover:bg-teal/90 text-white py-3 px-8 rounded-lg transition-colors duration-300 font-medium">
                    Load More Dishes
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="footer" class="bg-teal text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-poppins font-bold mb-4">LiberateBites</h3>
                    <p class="text-white/80 mb-6">Nourishing body and mind with mood-enhancing vegetarian cuisine.</p>
                    <div class="flex space-x-4">
                        <span class="text-white hover:text-yellow transition-colors duration-300 cursor-pointer">
                            <i class="fa-brands fa-instagram text-xl"></i>
                        </span>
                        <span class="text-white hover:text-yellow transition-colors duration-300 cursor-pointer">
                            <i class="fa-brands fa-facebook text-xl"></i>
                        </span>
                        <span class="text-white hover:text-yellow transition-colors duration-300 cursor-pointer">
                            <i class="fa-brands fa-twitter text-xl"></i>
                        </span>
                        <span class="text-white hover:text-yellow transition-colors duration-300 cursor-pointer">
                            <i class="fa-brands fa-pinterest text-xl"></i>
                        </span>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-white/80">
                        <li><span class="hover:text-yellow transition-colors duration-300 cursor-pointer">Home</span></li>
                        <li><span class="hover:text-yellow transition-colors duration-300 cursor-pointer">Menu</span></li>
                        <li><span class="hover:text-yellow transition-colors duration-300 cursor-pointer">About Us</span></li>
                        <li><span class="hover:text-yellow transition-colors duration-300 cursor-pointer">Mood Quiz</span></li>
                        <li><span class="hover:text-yellow transition-colors duration-300 cursor-pointer">Contact</span></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact Us</h4>
                    <ul class="space-y-2 text-white/80">
                        <li class="flex items-start">
                            <i class="fa-solid fa-location-dot mt-1 mr-3"></i>
                            <span>123 Wellness Way, Mindful City, MC 12345</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fa-solid fa-phone mr-3"></i>
                            <span>(*************</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fa-solid fa-envelope mr-3"></i>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
                    <p class="text-white/80 mb-4">Subscribe for mood-food pairings and exclusive recipes.</p>
                    <form class="flex">
                        <input type="email" placeholder="Your email" class="px-4 py-2 rounded-l-lg w-full focus:outline-none text-gray-800">
                        <button type="submit" class="bg-coral hover:bg-yellow text-white px-4 py-2 rounded-r-lg transition-colors duration-300">
                            <i class="fa-solid fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="border-t border-white/20 mt-8 pt-8 text-center text-white/60 text-sm">
                <p>© 2023 LiberateBites. All rights reserved. Nourish your mood, feed your soul.</p>
            </div>
        </div>
    </footer>

    <script>
        // Simple hover effect for food cards
        document.addEventListener('DOMContentLoaded', function() {
            const moodTabs = document.querySelectorAll('.mood-tab');
            
            moodTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    moodTabs.forEach(t => t.classList.remove('active', 'bg-coral', 'text-white'));
                    moodTabs.forEach(t => t.classList.add('bg-white/90', 'text-gray-700'));
                    this.classList.remove('bg-white/90', 'text-gray-700');
                    this.classList.add('active', 'bg-coral', 'text-white');
                });
            });
        });
    </script>

</body>
</html>