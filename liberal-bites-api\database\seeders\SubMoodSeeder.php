<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubMood;
use App\Models\Mood;

class SubMoodSeeder extends Seeder
{
    public function run(): void
    {
        $subMoods = [];

        foreach (Mood::all() as $mood) {
            for ($i = 1; $i <= 5; $i++) {
                $subMoods[] = [
                    'mood_id' => $mood->id,
                    'name' => $mood->name . ' SubMood ' . $i,
                    'meaning' => 'This is a sample description for ' . $mood->name . ' SubMood ' . $i,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        SubMood::insert($subMoods);
    }
}

